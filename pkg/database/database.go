package database

import (
	"fmt"
	"log"
	"time"

	"order-service/internal/config"
	"order-service/internal/models"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database wraps the GORM database connection
type Database struct {
	DB *gorm.DB
}

// New creates a new database connection
func New(cfg *config.DatabaseConfig) (*Database, error) {
	// Configure GORM logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(cfg.GetDSN()), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &Database{DB: db}, nil
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// AutoMigrate runs GORM auto-migration for all models
func (d *Database) AutoMigrate() error {
	return d.DB.AutoMigrate(
		&models.Customer{},
		&models.Product{},
		&models.ProductVariant{},
		&models.Order{},
		&models.OrderItem{},
		&models.OrderStatusHistory{},
	)
}

// RunMigrations runs database migrations using golang-migrate
func (d *Database) RunMigrations(migrationsPath string) error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create postgres driver: %w", err)
	}

	m, err := migrate.NewWithDatabaseInstance(migrationsPath, "postgres", driver)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// HealthCheck checks if the database is healthy
func (d *Database) HealthCheck() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// GetStats returns database connection statistics
func (d *Database) GetStats() map[string]interface{} {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}
}

// Transaction executes a function within a database transaction
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// BeginTransaction starts a new transaction
func (d *Database) BeginTransaction() *gorm.DB {
	return d.DB.Begin()
}

// SeedData seeds the database with initial data
func (d *Database) SeedData() error {
	// Check if data already exists
	var customerCount int64
	d.DB.Model(&models.Customer{}).Count(&customerCount)
	if customerCount > 0 {
		log.Println("Database already contains data, skipping seed")
		return nil
	}

	// Seed customers
	customers := []models.Customer{
		{
			Email:     "<EMAIL>",
			FirstName: "John",
			LastName:  "Doe",
			Phone:     "+1234567890",
			DefaultAddress: models.Address{
				FirstName:  "John",
				LastName:   "Doe",
				Address1:   "123 Main St",
				City:       "New York",
				Province:   "NY",
				Country:    "US",
				PostalCode: "10001",
				Phone:      "+1234567890",
			},
			AcceptsMarketing: true,
			State:           "enabled",
		},
		{
			Email:     "<EMAIL>",
			FirstName: "Jane",
			LastName:  "Smith",
			Phone:     "+1987654321",
			DefaultAddress: models.Address{
				FirstName:  "Jane",
				LastName:   "Smith",
				Address1:   "456 Oak Ave",
				City:       "Los Angeles",
				Province:   "CA",
				Country:    "US",
				PostalCode: "90210",
				Phone:      "+1987654321",
			},
			AcceptsMarketing: false,
			State:           "enabled",
		},
	}

	for _, customer := range customers {
		if err := d.DB.Create(&customer).Error; err != nil {
			return fmt.Errorf("failed to seed customer: %w", err)
		}
	}

	// Seed products
	products := []models.Product{
		{
			Title:       "Premium T-Shirt",
			Description: "High-quality cotton t-shirt",
			Handle:      "premium-t-shirt",
			Status:      "active",
			ProductType: "Apparel",
			Vendor:      "Fashion Co",
			Tags:        []string{"clothing", "t-shirt", "cotton"},
		},
		{
			Title:       "Wireless Headphones",
			Description: "Noise-cancelling wireless headphones",
			Handle:      "wireless-headphones",
			Status:      "active",
			ProductType: "Electronics",
			Vendor:      "Tech Corp",
			Tags:        []string{"electronics", "audio", "wireless"},
		},
	}

	for i, product := range products {
		if err := d.DB.Create(&product).Error; err != nil {
			return fmt.Errorf("failed to seed product: %w", err)
		}

		// Create variants for each product
		variants := []models.ProductVariant{
			{
				ProductID:           product.ID,
				Title:              "Small",
				SKU:                fmt.Sprintf("PROD-%d-S", i+1),
				Price:              29.99,
				InventoryQuantity:  100,
				Weight:             0.2,
				RequiresShipping:   true,
				Taxable:            true,
			},
			{
				ProductID:           product.ID,
				Title:              "Medium",
				SKU:                fmt.Sprintf("PROD-%d-M", i+1),
				Price:              29.99,
				InventoryQuantity:  150,
				Weight:             0.2,
				RequiresShipping:   true,
				Taxable:            true,
			},
		}

		for _, variant := range variants {
			if err := d.DB.Create(&variant).Error; err != nil {
				return fmt.Errorf("failed to seed product variant: %w", err)
			}
		}
	}

	log.Println("Database seeded successfully")
	return nil
}
