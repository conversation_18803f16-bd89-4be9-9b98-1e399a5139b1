package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// Validator wraps the go-playground validator
type Validator struct {
	validator *validator.Validate
}

// New creates a new validator instance
func New() *Validator {
	v := validator.New()
	
	// Register custom tag name function
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	// Register custom validators
	v.RegisterValidation("uuid", validateUUID)
	v.RegisterValidation("order_status", validateOrderStatus)
	v.RegisterValidation("fulfillment_status", validateFulfillmentStatus)
	v.RegisterValidation("shipping_status", validateShippingStatus)
	v.RegisterValidation("financial_status", validateFinancialStatus)

	return &Validator{validator: v}
}

// ValidateStruct validates a struct
func (v *Validator) ValidateStruct(obj interface{}) error {
	if err := v.validator.Struct(obj); err != nil {
		return formatValidationError(err)
	}
	return nil
}

// ValidateVar validates a single variable
func (v *Validator) ValidateVar(field interface{}, tag string) error {
	if err := v.validator.Var(field, tag); err != nil {
		return formatValidationError(err)
	}
	return nil
}

// formatValidationError formats validation errors into a readable format
func formatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var messages []string
		for _, e := range validationErrors {
			messages = append(messages, formatFieldError(e))
		}
		return fmt.Errorf("validation failed: %s", strings.Join(messages, ", "))
	}
	return err
}

// formatFieldError formats a single field validation error
func formatFieldError(e validator.FieldError) string {
	field := e.Field()
	tag := e.Tag()
	param := e.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "email":
		return fmt.Sprintf("%s must be a valid email address", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s", field, param)
	case "max":
		return fmt.Sprintf("%s must be at most %s", field, param)
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", field, param)
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", field)
	case "order_status":
		return fmt.Sprintf("%s must be a valid order status", field)
	case "fulfillment_status":
		return fmt.Sprintf("%s must be a valid fulfillment status", field)
	case "shipping_status":
		return fmt.Sprintf("%s must be a valid shipping status", field)
	case "financial_status":
		return fmt.Sprintf("%s must be a valid financial status", field)
	default:
		return fmt.Sprintf("%s failed validation for tag '%s'", field, tag)
	}
}

// Custom validation functions

// validateUUID validates UUID format
func validateUUID(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true // Allow empty values, use required tag for mandatory fields
	}
	
	// Simple UUID format validation (can be improved)
	parts := strings.Split(value, "-")
	if len(parts) != 5 {
		return false
	}
	
	expectedLengths := []int{8, 4, 4, 4, 12}
	for i, part := range parts {
		if len(part) != expectedLengths[i] {
			return false
		}
	}
	
	return true
}

// validateOrderStatus validates order status values
func validateOrderStatus(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	validStatuses := []string{
		"pending", "confirmed", "processing", "shipped", "delivered", "cancelled", "refunded",
	}
	
	for _, status := range validStatuses {
		if value == status {
			return true
		}
	}
	return false
}

// validateFulfillmentStatus validates fulfillment status values
func validateFulfillmentStatus(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	validStatuses := []string{
		"pending", "processing", "fulfilled", "partial", "cancelled",
	}
	
	for _, status := range validStatuses {
		if value == status {
			return true
		}
	}
	return false
}

// validateShippingStatus validates shipping status values
func validateShippingStatus(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	validStatuses := []string{
		"pending", "preparing", "shipped", "in_transit", "delivered", "returned",
	}
	
	for _, status := range validStatuses {
		if value == status {
			return true
		}
	}
	return false
}

// validateFinancialStatus validates financial status values
func validateFinancialStatus(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	validStatuses := []string{
		"pending", "authorized", "paid", "partial_paid", "refunded", "voided",
	}
	
	for _, status := range validStatuses {
		if value == status {
			return true
		}
	}
	return false
}

// SetupGinValidator sets up the validator for Gin
func SetupGinValidator() {
	v := New()
	binding.Validator = &ginValidator{validator: v}
}

// ginValidator implements gin's StructValidator interface
type ginValidator struct {
	validator *Validator
}

// ValidateStruct validates a struct for Gin
func (gv *ginValidator) ValidateStruct(obj interface{}) error {
	return gv.validator.ValidateStruct(obj)
}

// Engine returns the underlying validator engine
func (gv *ginValidator) Engine() interface{} {
	return gv.validator.validator
}

// Global validator instance
var globalValidator *Validator

// SetGlobalValidator sets the global validator instance
func SetGlobalValidator(v *Validator) {
	globalValidator = v
}

// GetGlobalValidator returns the global validator instance
func GetGlobalValidator() *Validator {
	if globalValidator == nil {
		globalValidator = New()
	}
	return globalValidator
}

// Validate validates a struct using the global validator
func Validate(obj interface{}) error {
	return GetGlobalValidator().ValidateStruct(obj)
}
