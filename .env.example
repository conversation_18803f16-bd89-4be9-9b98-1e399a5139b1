# Order Management Service Environment Configuration

# Application Configuration
APP_ENV=development
APP_NAME=order-service
APP_VERSION=1.0.0
APP_DEBUG=true

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=60

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=order_service
DB_USER=postgres
DB_PASSWORD=password
DB_SSL_MODE=disable
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=300

# Database URL (alternative to individual DB settings)
DATABASE_URL=postgres://postgres:password@localhost:5432/order_service?sslmode=disable

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# JWT Configuration (for authentication)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=24h
JWT_ISSUER=order-service

# CORS Configuration
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization,X-Request-ID
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=10

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PATH=/metrics
HEALTH_CHECK_PATH=/health

# External Services Configuration
# Payment Service
PAYMENT_SERVICE_URL=http://localhost:8081
PAYMENT_SERVICE_TIMEOUT=30s
PAYMENT_SERVICE_API_KEY=your-payment-service-api-key

# Inventory Service
INVENTORY_SERVICE_URL=http://localhost:8082
INVENTORY_SERVICE_TIMEOUT=30s
INVENTORY_SERVICE_API_KEY=your-inventory-service-api-key

# Notification Service
NOTIFICATION_SERVICE_URL=http://localhost:8083
NOTIFICATION_SERVICE_TIMEOUT=30s
NOTIFICATION_SERVICE_API_KEY=your-notification-service-api-key

# Email Configuration
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# AWS Configuration (if using AWS services)
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name

# Encryption Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Feature Flags
FEATURE_ANALYTICS_ENABLED=true
FEATURE_CACHING_ENABLED=true
FEATURE_ASYNC_PROCESSING_ENABLED=true

# Development Configuration
DEV_SEED_DATABASE=true
DEV_AUTO_MIGRATE=true
DEV_ENABLE_PROFILING=true

# Production Configuration
PROD_ENABLE_HTTPS=true
PROD_SSL_CERT_PATH=/etc/ssl/certs/server.crt
PROD_SSL_KEY_PATH=/etc/ssl/private/server.key

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# Security Configuration
SECURITY_BCRYPT_COST=12
SECURITY_SESSION_TIMEOUT=3600
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=900

# Cache Configuration
CACHE_TTL_DEFAULT=3600
CACHE_TTL_ORDERS=1800
CACHE_TTL_CUSTOMERS=7200

# Queue Configuration (if using message queues)
QUEUE_DRIVER=redis
QUEUE_CONNECTION=redis
QUEUE_FAILED_JOBS_TABLE=failed_jobs

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=30s
WEBHOOK_RETRY_ATTEMPTS=3

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_HOST=localhost:8080
SWAGGER_BASE_PATH=/api/v1

# Timezone Configuration
TZ=UTC

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf
UPLOAD_PATH=/uploads

# Pagination Configuration
PAGINATION_DEFAULT_LIMIT=20
PAGINATION_MAX_LIMIT=100

# Order Configuration
ORDER_NUMBER_PREFIX=ORD
ORDER_NUMBER_LENGTH=10
ORDER_EXPIRY_HOURS=24

# Currency Configuration
DEFAULT_CURRENCY=USD
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD

# Tax Configuration
TAX_CALCULATION_ENABLED=true
DEFAULT_TAX_RATE=0.08

# Shipping Configuration
SHIPPING_CALCULATION_ENABLED=true
FREE_SHIPPING_THRESHOLD=100.00

# Inventory Configuration
INVENTORY_CHECK_ENABLED=true
INVENTORY_RESERVE_ON_ORDER=true

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Service is under maintenance. Please try again later.
