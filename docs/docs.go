// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/health": {
            "get": {
                "description": "Comprehensive health check including database connectivity",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "monitoring"
                ],
                "summary": "Health check endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.HealthResponse"
                        }
                    },
                    "503": {
                        "description": "Service Unavailable",
                        "schema": {
                            "$ref": "#/definitions/handlers.HealthResponse"
                        }
                    }
                }
            }
        },
        "/live": {
            "get": {
                "description": "Check if service is alive",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "monitoring"
                ],
                "summary": "Liveness check endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/metrics": {
            "get": {
                "description": "Get application performance metrics",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "monitoring"
                ],
                "summary": "Application metrics endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.MetricsResponse"
                        }
                    }
                }
            }
        },
        "/orders": {
            "get": {
                "description": "List orders with optional filters",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "List orders",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Customer ID",
                        "name": "customer_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Customer Email",
                        "name": "customer_email",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Order Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Fulfillment Status",
                        "name": "fulfillment_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Shipping Status",
                        "name": "shipping_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Financial Status",
                        "name": "financial_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Start Date (RFC3339)",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End Date (RFC3339)",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "number",
                        "description": "Minimum Amount",
                        "name": "min_amount",
                        "in": "query"
                    },
                    {
                        "type": "number",
                        "description": "Maximum Amount",
                        "name": "max_amount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Limit",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "Sort By",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "DESC",
                        "description": "Sort Order",
                        "name": "sort_order",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ListOrdersResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new order with items",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Create a new order",
                "parameters": [
                    {
                        "description": "Order data",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.Order"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/items/{itemId}": {
            "put": {
                "description": "Update an existing order item",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update order item",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order Item ID",
                        "name": "itemId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Order item update data",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.UpdateOrderItemRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderItem"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Remove an item from an order",
                "tags": [
                    "orders"
                ],
                "summary": "Remove order item",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order Item ID",
                        "name": "itemId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/number/{orderNumber}": {
            "get": {
                "description": "Get an order by its order number",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Get an order by order number",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order Number",
                        "name": "orderNumber",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Order"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}": {
            "get": {
                "description": "Get an order by its UUID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Get an order by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Order"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "Update an existing order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update an order",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Order update data",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.UpdateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Order"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "Delete an order by ID",
                "tags": [
                    "orders"
                ],
                "summary": "Delete an order",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}/financial-status": {
            "patch": {
                "description": "Update the financial status of an order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update financial status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Status update data",
                        "name": "status",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.StatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}/fulfillment-status": {
            "patch": {
                "description": "Update the fulfillment status of an order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update fulfillment status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Status update data",
                        "name": "status",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.StatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}/items": {
            "post": {
                "description": "Add a new item to an existing order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Add item to order",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Order item data",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.AddOrderItemRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.OrderItem"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}/shipping-status": {
            "patch": {
                "description": "Update the shipping status of an order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update shipping status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Status update data",
                        "name": "status",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.StatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/orders/{id}/status": {
            "patch": {
                "description": "Update the status of an order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Update order status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Order ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Status update data",
                        "name": "status",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.StatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/ready": {
            "get": {
                "description": "Check if service is ready to accept requests",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "monitoring"
                ],
                "summary": "Readiness check endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "503": {
                        "description": "Service Unavailable",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "handlers.DatabaseMetrics": {
            "type": "object",
            "properties": {
                "idle": {
                    "type": "integer"
                },
                "in_use": {
                    "type": "integer"
                },
                "max_idle_closed": {
                    "type": "integer"
                },
                "max_lifetime_closed": {
                    "type": "integer"
                },
                "open_connections": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "wait_count": {
                    "type": "integer"
                },
                "wait_duration": {
                    "type": "string"
                }
            }
        },
        "handlers.ErrorResponse": {
            "type": "object",
            "properties": {
                "details": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "handlers.HTTPMetrics": {
            "type": "object",
            "properties": {
                "average_latency": {
                    "type": "string"
                },
                "requests_error": {
                    "type": "integer"
                },
                "requests_success": {
                    "type": "integer"
                },
                "requests_total": {
                    "type": "integer"
                }
            }
        },
        "handlers.HealthCheck": {
            "type": "object",
            "properties": {
                "latency": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "handlers.HealthResponse": {
            "type": "object",
            "properties": {
                "checks": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/handlers.HealthCheck"
                    }
                },
                "service": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "uptime": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "handlers.ListOrdersResponse": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer"
                },
                "offset": {
                    "type": "integer"
                },
                "orders": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Order"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "handlers.MetricsResponse": {
            "type": "object",
            "properties": {
                "custom": {
                    "type": "object",
                    "additionalProperties": true
                },
                "database": {
                    "$ref": "#/definitions/handlers.DatabaseMetrics"
                },
                "http": {
                    "$ref": "#/definitions/handlers.HTTPMetrics"
                },
                "runtime": {
                    "$ref": "#/definitions/handlers.RuntimeMetrics"
                },
                "service": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "handlers.RuntimeMetrics": {
            "type": "object",
            "properties": {
                "gc_runs": {
                    "type": "integer"
                },
                "go_version": {
                    "type": "string"
                },
                "last_gc_time": {
                    "type": "string"
                },
                "memory_alloc": {
                    "type": "integer"
                },
                "memory_sys": {
                    "type": "integer"
                },
                "memory_total": {
                    "type": "integer"
                },
                "next_gc_target": {
                    "type": "integer"
                },
                "num_cpu": {
                    "type": "integer"
                },
                "num_goroutines": {
                    "type": "integer"
                }
            }
        },
        "handlers.StatusUpdateRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "changed_by": {
                    "type": "string"
                },
                "comment": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "models.Address": {
            "type": "object",
            "properties": {
                "address1": {
                    "type": "string"
                },
                "address2": {
                    "type": "string"
                },
                "city": {
                    "type": "string"
                },
                "company": {
                    "type": "string"
                },
                "country": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string"
                },
                "last_name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "postal_code": {
                    "type": "string"
                },
                "province": {
                    "type": "string"
                }
            }
        },
        "models.FinancialStatus": {
            "type": "string",
            "enum": [
                "pending",
                "authorized",
                "paid",
                "partial_paid",
                "refunded",
                "voided"
            ],
            "x-enum-varnames": [
                "FinancialStatusPending",
                "FinancialStatusAuthorized",
                "FinancialStatusPaid",
                "FinancialStatusPartialPaid",
                "FinancialStatusRefunded",
                "FinancialStatusVoided"
            ]
        },
        "models.FulfillmentStatus": {
            "type": "string",
            "enum": [
                "pending",
                "processing",
                "fulfilled",
                "partial",
                "cancelled"
            ],
            "x-enum-varnames": [
                "FulfillmentStatusPending",
                "FulfillmentStatusProcessing",
                "FulfillmentStatusFulfilled",
                "FulfillmentStatusPartial",
                "FulfillmentStatusCancelled"
            ]
        },
        "models.Order": {
            "type": "object",
            "properties": {
                "billing_address": {
                    "description": "Addresses",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Address"
                        }
                    ]
                },
                "created_at": {
                    "description": "Timestamps",
                    "type": "string"
                },
                "currency": {
                    "type": "string"
                },
                "customer_email": {
                    "type": "string"
                },
                "customer_id": {
                    "type": "string"
                },
                "customer_phone": {
                    "type": "string"
                },
                "discount_amount": {
                    "type": "number"
                },
                "financial_status": {
                    "$ref": "#/definitions/models.FinancialStatus"
                },
                "fulfillment_status": {
                    "$ref": "#/definitions/models.FulfillmentStatus"
                },
                "id": {
                    "type": "string"
                },
                "items": {
                    "description": "Relationships",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderItem"
                    }
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "notes": {
                    "description": "Metadata",
                    "type": "string"
                },
                "order_number": {
                    "type": "string"
                },
                "shipping_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "shipping_amount": {
                    "type": "number"
                },
                "shipping_status": {
                    "$ref": "#/definitions/models.ShippingStatus"
                },
                "status": {
                    "$ref": "#/definitions/models.OrderStatus"
                },
                "status_history": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderStatusHistory"
                    }
                },
                "subtotal_amount": {
                    "description": "Pricing",
                    "type": "number"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tax_amount": {
                    "type": "number"
                },
                "total_amount": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.OrderItem": {
            "type": "object",
            "properties": {
                "compare_at_price": {
                    "type": "number"
                },
                "created_at": {
                    "description": "Timestamps",
                    "type": "string"
                },
                "fulfilled_quantity": {
                    "type": "integer"
                },
                "fulfillment_status": {
                    "description": "Fulfillment tracking",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.FulfillmentStatus"
                        }
                    ]
                },
                "id": {
                    "type": "string"
                },
                "order_id": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "product_id": {
                    "type": "string"
                },
                "product_title": {
                    "type": "string"
                },
                "properties": {
                    "description": "Metadata",
                    "type": "object",
                    "additionalProperties": true
                },
                "quantity": {
                    "type": "integer"
                },
                "requires_shipping": {
                    "type": "boolean"
                },
                "sku": {
                    "type": "string"
                },
                "taxable": {
                    "type": "boolean"
                },
                "total_price": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                },
                "variant_id": {
                    "type": "string"
                },
                "variant_title": {
                    "type": "string"
                },
                "weight": {
                    "description": "Product details",
                    "type": "number"
                },
                "weight_unit": {
                    "type": "string"
                }
            }
        },
        "models.OrderStatus": {
            "type": "string",
            "enum": [
                "pending",
                "confirmed",
                "processing",
                "shipped",
                "delivered",
                "cancelled",
                "refunded"
            ],
            "x-enum-varnames": [
                "OrderStatusPending",
                "OrderStatusConfirmed",
                "OrderStatusProcessing",
                "OrderStatusShipped",
                "OrderStatusDelivered",
                "OrderStatusCancelled",
                "OrderStatusRefunded"
            ]
        },
        "models.OrderStatusHistory": {
            "type": "object",
            "properties": {
                "changed_by": {
                    "type": "string"
                },
                "changed_by_type": {
                    "description": "user, system, api",
                    "type": "string"
                },
                "comment": {
                    "type": "string"
                },
                "created_at": {
                    "description": "Timestamps",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "new_status": {
                    "type": "string"
                },
                "order_id": {
                    "type": "string"
                },
                "previous_status": {
                    "type": "string"
                },
                "status_type": {
                    "description": "order, fulfillment, shipping, financial",
                    "type": "string"
                }
            }
        },
        "models.ShippingStatus": {
            "type": "string",
            "enum": [
                "pending",
                "preparing",
                "shipped",
                "in_transit",
                "delivered",
                "returned"
            ],
            "x-enum-varnames": [
                "ShippingStatusPending",
                "ShippingStatusPreparing",
                "ShippingStatusShipped",
                "ShippingStatusInTransit",
                "ShippingStatusDelivered",
                "ShippingStatusReturned"
            ]
        },
        "services.AddOrderItemRequest": {
            "type": "object",
            "required": [
                "price",
                "product_id",
                "quantity"
            ],
            "properties": {
                "price": {
                    "type": "number",
                    "minimum": 0
                },
                "product_id": {
                    "type": "string"
                },
                "properties": {
                    "type": "object",
                    "additionalProperties": true
                },
                "quantity": {
                    "type": "integer",
                    "minimum": 1
                },
                "variant_id": {
                    "type": "string"
                }
            }
        },
        "services.CreateOrderItemRequest": {
            "type": "object",
            "required": [
                "price",
                "product_id",
                "quantity"
            ],
            "properties": {
                "price": {
                    "type": "number",
                    "minimum": 0
                },
                "product_id": {
                    "type": "string"
                },
                "properties": {
                    "type": "object",
                    "additionalProperties": true
                },
                "quantity": {
                    "type": "integer",
                    "minimum": 1
                },
                "variant_id": {
                    "type": "string"
                }
            }
        },
        "services.CreateOrderRequest": {
            "type": "object",
            "required": [
                "billing_address",
                "currency",
                "customer_email",
                "customer_id",
                "items",
                "shipping_address"
            ],
            "properties": {
                "billing_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "currency": {
                    "type": "string"
                },
                "customer_email": {
                    "type": "string"
                },
                "customer_id": {
                    "type": "string"
                },
                "customer_phone": {
                    "type": "string"
                },
                "discount_amount": {
                    "type": "number"
                },
                "items": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/services.CreateOrderItemRequest"
                    }
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "notes": {
                    "type": "string"
                },
                "shipping_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "shipping_amount": {
                    "type": "number"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tax_amount": {
                    "type": "number"
                }
            }
        },
        "services.UpdateOrderItemRequest": {
            "type": "object",
            "properties": {
                "price": {
                    "type": "number",
                    "minimum": 0
                },
                "properties": {
                    "type": "object",
                    "additionalProperties": true
                },
                "quantity": {
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "services.UpdateOrderRequest": {
            "type": "object",
            "properties": {
                "billing_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "customer_email": {
                    "type": "string"
                },
                "customer_phone": {
                    "type": "string"
                },
                "discount_amount": {
                    "type": "number"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "notes": {
                    "type": "string"
                },
                "shipping_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "shipping_amount": {
                    "type": "number"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tax_amount": {
                    "type": "number"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Order Management Service API",
	Description:      "A comprehensive order management microservice for ecommerce applications",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
