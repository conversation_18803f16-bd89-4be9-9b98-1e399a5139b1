basePath: /api/v1
definitions:
  handlers.DatabaseMetrics:
    properties:
      idle:
        type: integer
      in_use:
        type: integer
      max_idle_closed:
        type: integer
      max_lifetime_closed:
        type: integer
      open_connections:
        type: integer
      status:
        type: string
      wait_count:
        type: integer
      wait_duration:
        type: string
    type: object
  handlers.ErrorResponse:
    properties:
      details:
        type: string
      error:
        type: string
      request_id:
        type: string
    type: object
  handlers.HTTPMetrics:
    properties:
      average_latency:
        type: string
      requests_error:
        type: integer
      requests_success:
        type: integer
      requests_total:
        type: integer
    type: object
  handlers.HealthCheck:
    properties:
      latency:
        type: string
      message:
        type: string
      status:
        type: string
    type: object
  handlers.HealthResponse:
    properties:
      checks:
        additionalProperties:
          $ref: '#/definitions/handlers.HealthCheck'
        type: object
      service:
        type: string
      status:
        type: string
      timestamp:
        type: string
      uptime:
        type: string
      version:
        type: string
    type: object
  handlers.ListOrdersResponse:
    properties:
      limit:
        type: integer
      offset:
        type: integer
      orders:
        items:
          $ref: '#/definitions/models.Order'
        type: array
      total:
        type: integer
    type: object
  handlers.MetricsResponse:
    properties:
      custom:
        additionalProperties: true
        type: object
      database:
        $ref: '#/definitions/handlers.DatabaseMetrics'
      http:
        $ref: '#/definitions/handlers.HTTPMetrics'
      runtime:
        $ref: '#/definitions/handlers.RuntimeMetrics'
      service:
        type: string
      timestamp:
        type: string
    type: object
  handlers.RuntimeMetrics:
    properties:
      gc_runs:
        type: integer
      go_version:
        type: string
      last_gc_time:
        type: string
      memory_alloc:
        type: integer
      memory_sys:
        type: integer
      memory_total:
        type: integer
      next_gc_target:
        type: integer
      num_cpu:
        type: integer
      num_goroutines:
        type: integer
    type: object
  handlers.StatusUpdateRequest:
    properties:
      changed_by:
        type: string
      comment:
        type: string
      status:
        type: string
    required:
    - status
    type: object
  models.Address:
    properties:
      address1:
        type: string
      address2:
        type: string
      city:
        type: string
      company:
        type: string
      country:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      phone:
        type: string
      postal_code:
        type: string
      province:
        type: string
    type: object
  models.FinancialStatus:
    enum:
    - pending
    - authorized
    - paid
    - partial_paid
    - refunded
    - voided
    type: string
    x-enum-varnames:
    - FinancialStatusPending
    - FinancialStatusAuthorized
    - FinancialStatusPaid
    - FinancialStatusPartialPaid
    - FinancialStatusRefunded
    - FinancialStatusVoided
  models.FulfillmentStatus:
    enum:
    - pending
    - processing
    - fulfilled
    - partial
    - cancelled
    type: string
    x-enum-varnames:
    - FulfillmentStatusPending
    - FulfillmentStatusProcessing
    - FulfillmentStatusFulfilled
    - FulfillmentStatusPartial
    - FulfillmentStatusCancelled
  models.Order:
    properties:
      billing_address:
        allOf:
        - $ref: '#/definitions/models.Address'
        description: Addresses
      created_at:
        description: Timestamps
        type: string
      currency:
        type: string
      customer_email:
        type: string
      customer_id:
        type: string
      customer_phone:
        type: string
      discount_amount:
        type: number
      financial_status:
        $ref: '#/definitions/models.FinancialStatus'
      fulfillment_status:
        $ref: '#/definitions/models.FulfillmentStatus'
      id:
        type: string
      items:
        description: Relationships
        items:
          $ref: '#/definitions/models.OrderItem'
        type: array
      metadata:
        additionalProperties: true
        type: object
      notes:
        description: Metadata
        type: string
      order_number:
        type: string
      shipping_address:
        $ref: '#/definitions/models.Address'
      shipping_amount:
        type: number
      shipping_status:
        $ref: '#/definitions/models.ShippingStatus'
      status:
        $ref: '#/definitions/models.OrderStatus'
      status_history:
        items:
          $ref: '#/definitions/models.OrderStatusHistory'
        type: array
      subtotal_amount:
        description: Pricing
        type: number
      tags:
        items:
          type: string
        type: array
      tax_amount:
        type: number
      total_amount:
        type: number
      updated_at:
        type: string
    type: object
  models.OrderItem:
    properties:
      compare_at_price:
        type: number
      created_at:
        description: Timestamps
        type: string
      fulfilled_quantity:
        type: integer
      fulfillment_status:
        allOf:
        - $ref: '#/definitions/models.FulfillmentStatus'
        description: Fulfillment tracking
      id:
        type: string
      order_id:
        type: string
      price:
        type: number
      product_id:
        type: string
      product_title:
        type: string
      properties:
        additionalProperties: true
        description: Metadata
        type: object
      quantity:
        type: integer
      requires_shipping:
        type: boolean
      sku:
        type: string
      taxable:
        type: boolean
      total_price:
        type: number
      updated_at:
        type: string
      variant_id:
        type: string
      variant_title:
        type: string
      weight:
        description: Product details
        type: number
      weight_unit:
        type: string
    type: object
  models.OrderStatus:
    enum:
    - pending
    - confirmed
    - processing
    - shipped
    - delivered
    - cancelled
    - refunded
    type: string
    x-enum-varnames:
    - OrderStatusPending
    - OrderStatusConfirmed
    - OrderStatusProcessing
    - OrderStatusShipped
    - OrderStatusDelivered
    - OrderStatusCancelled
    - OrderStatusRefunded
  models.OrderStatusHistory:
    properties:
      changed_by:
        type: string
      changed_by_type:
        description: user, system, api
        type: string
      comment:
        type: string
      created_at:
        description: Timestamps
        type: string
      id:
        type: string
      new_status:
        type: string
      order_id:
        type: string
      previous_status:
        type: string
      status_type:
        description: order, fulfillment, shipping, financial
        type: string
    type: object
  models.ShippingStatus:
    enum:
    - pending
    - preparing
    - shipped
    - in_transit
    - delivered
    - returned
    type: string
    x-enum-varnames:
    - ShippingStatusPending
    - ShippingStatusPreparing
    - ShippingStatusShipped
    - ShippingStatusInTransit
    - ShippingStatusDelivered
    - ShippingStatusReturned
  services.AddOrderItemRequest:
    properties:
      price:
        minimum: 0
        type: number
      product_id:
        type: string
      properties:
        additionalProperties: true
        type: object
      quantity:
        minimum: 1
        type: integer
      variant_id:
        type: string
    required:
    - price
    - product_id
    - quantity
    type: object
  services.CreateOrderItemRequest:
    properties:
      price:
        minimum: 0
        type: number
      product_id:
        type: string
      properties:
        additionalProperties: true
        type: object
      quantity:
        minimum: 1
        type: integer
      variant_id:
        type: string
    required:
    - price
    - product_id
    - quantity
    type: object
  services.CreateOrderRequest:
    properties:
      billing_address:
        $ref: '#/definitions/models.Address'
      currency:
        type: string
      customer_email:
        type: string
      customer_id:
        type: string
      customer_phone:
        type: string
      discount_amount:
        type: number
      items:
        items:
          $ref: '#/definitions/services.CreateOrderItemRequest'
        minItems: 1
        type: array
      metadata:
        additionalProperties: true
        type: object
      notes:
        type: string
      shipping_address:
        $ref: '#/definitions/models.Address'
      shipping_amount:
        type: number
      tags:
        items:
          type: string
        type: array
      tax_amount:
        type: number
    required:
    - billing_address
    - currency
    - customer_email
    - customer_id
    - items
    - shipping_address
    type: object
  services.UpdateOrderItemRequest:
    properties:
      price:
        minimum: 0
        type: number
      properties:
        additionalProperties: true
        type: object
      quantity:
        minimum: 1
        type: integer
    type: object
  services.UpdateOrderRequest:
    properties:
      billing_address:
        $ref: '#/definitions/models.Address'
      customer_email:
        type: string
      customer_phone:
        type: string
      discount_amount:
        type: number
      metadata:
        additionalProperties: true
        type: object
      notes:
        type: string
      shipping_address:
        $ref: '#/definitions/models.Address'
      shipping_amount:
        type: number
      tags:
        items:
          type: string
        type: array
      tax_amount:
        type: number
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.example.com/support
  description: A comprehensive order management microservice for ecommerce applications
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Order Management Service API
  version: "1.0"
paths:
  /health:
    get:
      description: Comprehensive health check including database connectivity
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.HealthResponse'
        "503":
          description: Service Unavailable
          schema:
            $ref: '#/definitions/handlers.HealthResponse'
      summary: Health check endpoint
      tags:
      - monitoring
  /live:
    get:
      description: Check if service is alive
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Liveness check endpoint
      tags:
      - monitoring
  /metrics:
    get:
      description: Get application performance metrics
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.MetricsResponse'
      summary: Application metrics endpoint
      tags:
      - monitoring
  /orders:
    get:
      description: List orders with optional filters
      parameters:
      - description: Customer ID
        in: query
        name: customer_id
        type: string
      - description: Customer Email
        in: query
        name: customer_email
        type: string
      - description: Order Status
        in: query
        name: status
        type: string
      - description: Fulfillment Status
        in: query
        name: fulfillment_status
        type: string
      - description: Shipping Status
        in: query
        name: shipping_status
        type: string
      - description: Financial Status
        in: query
        name: financial_status
        type: string
      - description: Start Date (RFC3339)
        in: query
        name: start_date
        type: string
      - description: End Date (RFC3339)
        in: query
        name: end_date
        type: string
      - description: Minimum Amount
        in: query
        name: min_amount
        type: number
      - description: Maximum Amount
        in: query
        name: max_amount
        type: number
      - default: 20
        description: Limit
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset
        in: query
        name: offset
        type: integer
      - default: created_at
        description: Sort By
        in: query
        name: sort_by
        type: string
      - default: DESC
        description: Sort Order
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ListOrdersResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: List orders
      tags:
      - orders
    post:
      consumes:
      - application/json
      description: Create a new order with items
      parameters:
      - description: Order data
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/services.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Order'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Create a new order
      tags:
      - orders
  /orders/{id}:
    delete:
      description: Delete an order by ID
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Delete an order
      tags:
      - orders
    get:
      description: Get an order by its UUID
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Order'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Get an order by ID
      tags:
      - orders
    put:
      consumes:
      - application/json
      description: Update an existing order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Order update data
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/services.UpdateOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Order'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update an order
      tags:
      - orders
  /orders/{id}/financial-status:
    patch:
      consumes:
      - application/json
      description: Update the financial status of an order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Status update data
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/handlers.StatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update financial status
      tags:
      - orders
  /orders/{id}/fulfillment-status:
    patch:
      consumes:
      - application/json
      description: Update the fulfillment status of an order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Status update data
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/handlers.StatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update fulfillment status
      tags:
      - orders
  /orders/{id}/items:
    post:
      consumes:
      - application/json
      description: Add a new item to an existing order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Order item data
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/services.AddOrderItemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.OrderItem'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Add item to order
      tags:
      - orders
  /orders/{id}/shipping-status:
    patch:
      consumes:
      - application/json
      description: Update the shipping status of an order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Status update data
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/handlers.StatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update shipping status
      tags:
      - orders
  /orders/{id}/status:
    patch:
      consumes:
      - application/json
      description: Update the status of an order
      parameters:
      - description: Order ID
        in: path
        name: id
        required: true
        type: string
      - description: Status update data
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/handlers.StatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update order status
      tags:
      - orders
  /orders/items/{itemId}:
    delete:
      description: Remove an item from an order
      parameters:
      - description: Order Item ID
        in: path
        name: itemId
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Remove order item
      tags:
      - orders
    put:
      consumes:
      - application/json
      description: Update an existing order item
      parameters:
      - description: Order Item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Order item update data
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/services.UpdateOrderItemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OrderItem'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Update order item
      tags:
      - orders
  /orders/number/{orderNumber}:
    get:
      description: Get an order by its order number
      parameters:
      - description: Order Number
        in: path
        name: orderNumber
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Order'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Get an order by order number
      tags:
      - orders
  /ready:
    get:
      description: Check if service is ready to accept requests
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "503":
          description: Service Unavailable
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Readiness check endpoint
      tags:
      - monitoring
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
