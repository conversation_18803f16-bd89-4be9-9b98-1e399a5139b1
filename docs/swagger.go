// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/orders": {
            "get": {
                "description": "List orders with optional filters",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "List orders",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Customer ID",
                        "name": "customer_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Customer Email",
                        "name": "customer_email",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Order Status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Fulfillment Status",
                        "name": "fulfillment_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Shipping Status",
                        "name": "shipping_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Financial Status",
                        "name": "financial_status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Start Date (RFC3339)",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End Date (RFC3339)",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "number",
                        "description": "Minimum Amount",
                        "name": "min_amount",
                        "in": "query"
                    },
                    {
                        "type": "number",
                        "description": "Maximum Amount",
                        "name": "max_amount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Limit",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "Offset",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "Sort By",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "DESC",
                        "description": "Sort Order",
                        "name": "sort_order",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ListOrdersResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new order with items",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "orders"
                ],
                "summary": "Create a new order",
                "parameters": [
                    {
                        "description": "Order data",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.Order"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "handlers.ErrorResponse": {
            "type": "object",
            "properties": {
                "details": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "request_id": {
                    "type": "string"
                }
            }
        },
        "handlers.ListOrdersResponse": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer"
                },
                "offset": {
                    "type": "integer"
                },
                "orders": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Order"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.Order": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "order_number": {
                    "type": "string"
                },
                "customer_id": {
                    "type": "string"
                },
                "customer_email": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "total_amount": {
                    "type": "number"
                },
                "created_at": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "services.CreateOrderRequest": {
            "type": "object",
            "required": [
                "customer_id",
                "customer_email",
                "billing_address",
                "shipping_address",
                "items",
                "currency"
            ],
            "properties": {
                "customer_id": {
                    "type": "string"
                },
                "customer_email": {
                    "type": "string"
                },
                "billing_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "shipping_address": {
                    "$ref": "#/definitions/models.Address"
                },
                "items": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/services.CreateOrderItemRequest"
                    }
                },
                "currency": {
                    "type": "string",
                    "maxLength": 3,
                    "minLength": 3
                },
                "tax_amount": {
                    "type": "number"
                },
                "shipping_amount": {
                    "type": "number"
                },
                "discount_amount": {
                    "type": "number"
                },
                "notes": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Order Management Service API",
	Description:      "A comprehensive order management microservice for ecommerce applications",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
