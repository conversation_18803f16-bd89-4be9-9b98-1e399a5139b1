# Order Management Service API Reference

## Overview

The Order Management Service provides a comprehensive REST API for managing ecommerce orders. This document provides detailed information about all available endpoints, request/response formats, and usage examples.

## Base URL

- **Local Development**: `http://localhost:8080/api/v1`
- **Docker**: `http://localhost:8080/api/v1`

## Authentication

All API endpoints require authentication via <PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Content Type

All requests should use `application/json` content type:

```
Content-Type: application/json
```

## Error Handling

The API uses standard HTTP status codes and returns error responses in the following format:

```json
{
  "error": "Error message",
  "details": "Detailed error description",
  "request_id": "unique-request-identifier"
}
```

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

## Orders API

### Create Order

Create a new order with items.

**Endpoint**: `POST /orders`

**Request Body**:
```json
{
  "customer_id": "123e4567-e89b-12d3-a456-************",
  "customer_email": "<EMAIL>",
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "company": "Acme Corp",
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "New York",
    "province": "NY",
    "country": "US",
    "zip": "10001",
    "phone": "******-123-4567"
  },
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "company": "Acme Corp",
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "New York",
    "province": "NY",
    "country": "US",
    "zip": "10001",
    "phone": "******-123-4567"
  },
  "items": [
    {
      "product_id": "456e7890-e89b-12d3-a456-************",
      "variant_id": "789e0123-e89b-12d3-a456-************",
      "quantity": 2,
      "price": 29.99,
      "title": "Product Title",
      "variant_title": "Size: Large, Color: Blue"
    }
  ],
  "currency": "USD",
  "tax_amount": 4.80,
  "shipping_amount": 9.99,
  "discount_amount": 5.00,
  "notes": "Special delivery instructions",
  "tags": ["priority", "gift"]
}
```

**Response**: `201 Created`
```json
{
  "id": "order-uuid",
  "order_number": "ORD-1234567890",
  "customer_id": "123e4567-e89b-12d3-a456-************",
  "customer_email": "<EMAIL>",
  "status": "pending",
  "fulfillment_status": "unfulfilled",
  "shipping_status": "unshipped",
  "financial_status": "pending",
  "total_amount": 69.78,
  "subtotal_amount": 59.98,
  "tax_amount": 4.80,
  "shipping_amount": 9.99,
  "discount_amount": 5.00,
  "currency": "USD",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### List Orders

Retrieve a list of orders with optional filtering and pagination.

**Endpoint**: `GET /orders`

**Query Parameters**:
- `customer_id` (string) - Filter by customer ID
- `customer_email` (string) - Filter by customer email
- `status` (string) - Filter by order status
- `fulfillment_status` (string) - Filter by fulfillment status
- `shipping_status` (string) - Filter by shipping status
- `financial_status` (string) - Filter by financial status
- `start_date` (string) - Filter orders created after this date (RFC3339)
- `end_date` (string) - Filter orders created before this date (RFC3339)
- `min_amount` (number) - Filter orders with total amount >= this value
- `max_amount` (number) - Filter orders with total amount <= this value
- `limit` (integer) - Number of results to return (default: 20, max: 100)
- `offset` (integer) - Number of results to skip (default: 0)
- `sort_by` (string) - Field to sort by (default: created_at)
- `sort_order` (string) - Sort order: ASC or DESC (default: DESC)

**Example Request**:
```
GET /orders?status=pending&limit=10&sort_by=created_at&sort_order=DESC
```

**Response**: `200 OK`
```json
{
  "orders": [
    {
      "id": "order-uuid",
      "order_number": "ORD-1234567890",
      "customer_id": "123e4567-e89b-12d3-a456-************",
      "customer_email": "<EMAIL>",
      "status": "pending",
      "total_amount": 69.78,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

### Get Order by ID

Retrieve a specific order by its ID.

**Endpoint**: `GET /orders/{id}`

**Response**: `200 OK`
```json
{
  "id": "order-uuid",
  "order_number": "ORD-1234567890",
  "customer_id": "123e4567-e89b-12d3-a456-************",
  "customer_email": "<EMAIL>",
  "status": "pending",
  "fulfillment_status": "unfulfilled",
  "shipping_status": "unshipped",
  "financial_status": "pending",
  "total_amount": 69.78,
  "items": [
    {
      "id": "item-uuid",
      "product_id": "456e7890-e89b-12d3-a456-************",
      "variant_id": "789e0123-e89b-12d3-a456-************",
      "quantity": 2,
      "price": 29.99,
      "title": "Product Title",
      "variant_title": "Size: Large, Color: Blue"
    }
  ],
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address1": "123 Main St",
    "city": "New York",
    "country": "US"
  },
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address1": "123 Main St",
    "city": "New York",
    "country": "US"
  },
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### Get Order by Number

Retrieve a specific order by its order number.

**Endpoint**: `GET /orders/number/{orderNumber}`

**Response**: Same as Get Order by ID

### Update Order

Update an existing order.

**Endpoint**: `PUT /orders/{id}`

**Request Body**: Same as Create Order (partial updates supported)

**Response**: `200 OK` - Updated order object

### Delete Order

Soft delete an order (marks as deleted but preserves data).

**Endpoint**: `DELETE /orders/{id}`

**Response**: `204 No Content`

## Status Management

### Update Order Status

Update the main order status.

**Endpoint**: `PATCH /orders/{id}/status`

**Request Body**:
```json
{
  "status": "confirmed",
  "notes": "Order confirmed by customer service"
}
```

**Valid Status Values**:
- `pending` - Order is pending confirmation
- `confirmed` - Order has been confirmed
- `processing` - Order is being processed
- `shipped` - Order has been shipped
- `delivered` - Order has been delivered
- `cancelled` - Order has been cancelled

**Response**: `200 OK`
```json
{
  "message": "Order status updated successfully",
  "order": {
    "id": "order-uuid",
    "status": "confirmed",
    "updated_at": "2024-01-01T12:30:00Z"
  }
}
```

### Update Fulfillment Status

**Endpoint**: `PATCH /orders/{id}/fulfillment-status`

**Valid Status Values**:
- `unfulfilled` - No items have been fulfilled
- `partial` - Some items have been fulfilled
- `fulfilled` - All items have been fulfilled
- `restocked` - Items have been restocked

### Update Shipping Status

**Endpoint**: `PATCH /orders/{id}/shipping-status`

**Valid Status Values**:
- `unshipped` - No items have been shipped
- `partial` - Some items have been shipped
- `shipped` - All items have been shipped
- `delivered` - Order has been delivered

### Update Financial Status

**Endpoint**: `PATCH /orders/{id}/financial-status`

**Valid Status Values**:
- `pending` - Payment is pending
- `authorized` - Payment has been authorized
- `paid` - Payment has been completed
- `refunded` - Payment has been refunded
- `voided` - Payment has been voided

## Order Items Management

### Add Item to Order

Add a new item to an existing order.

**Endpoint**: `POST /orders/{id}/items`

**Request Body**:
```json
{
  "product_id": "456e7890-e89b-12d3-a456-************",
  "variant_id": "789e0123-e89b-12d3-a456-************",
  "quantity": 1,
  "price": 39.99,
  "title": "New Product",
  "variant_title": "Size: Medium"
}
```

**Response**: `201 Created` - Created order item object

### Update Order Item

Update an existing order item.

**Endpoint**: `PUT /orders/items/{itemId}`

**Request Body**: Same as Add Item to Order

**Response**: `200 OK` - Updated order item object

### Remove Order Item

Remove an item from an order.

**Endpoint**: `DELETE /orders/items/{itemId}`

**Response**: `204 No Content`

## Health and Monitoring

### Health Check

Comprehensive health check including database connectivity.

**Endpoint**: `GET /health`

**Response**: `200 OK` (healthy) or `503 Service Unavailable` (unhealthy)
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "order-service",
  "version": "1.0.0",
  "uptime": "2h30m15s",
  "checks": {
    "database": {
      "status": "healthy",
      "message": "Database connection successful",
      "latency": "5ms"
    },
    "memory": {
      "status": "healthy",
      "message": "Memory usage normal"
    }
  }
}
```

### Readiness Check

Check if service is ready to accept requests.

**Endpoint**: `GET /ready`

### Liveness Check

Check if service is alive.

**Endpoint**: `GET /live`

### Application Metrics

Get application performance metrics.

**Endpoint**: `GET /metrics`

**Response**: `200 OK`
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "order-service",
  "runtime": {
    "go_version": "go1.21.0",
    "num_goroutines": 25,
    "memory_alloc": 1048576,
    "gc_runs": 5
  },
  "database": {
    "status": "healthy",
    "open_connections": 5,
    "in_use": 2,
    "idle": 3
  },
  "http": {
    "requests_total": 1000,
    "requests_success": 950,
    "requests_error": 50
  }
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Default**: 100 requests per minute per IP
- **Burst**: 10 requests
- **Headers**: Rate limit information is included in response headers

## Pagination

List endpoints support pagination:
- `limit`: Number of results (default: 20, max: 100)
- `offset`: Number of results to skip (default: 0)

## Filtering and Sorting

List endpoints support filtering and sorting:
- Multiple filters can be combined
- Sort by any field with ASC/DESC order
- Date filters use RFC3339 format

## Examples

### Create a Simple Order

```bash
curl -X POST http://localhost:8080/api/v1/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "customer_id": "123e4567-e89b-12d3-a456-************",
    "customer_email": "<EMAIL>",
    "billing_address": {
      "first_name": "John",
      "last_name": "Doe",
      "address1": "123 Main St",
      "city": "New York",
      "country": "US",
      "zip": "10001"
    },
    "shipping_address": {
      "first_name": "John",
      "last_name": "Doe",
      "address1": "123 Main St",
      "city": "New York",
      "country": "US",
      "zip": "10001"
    },
    "items": [
      {
        "product_id": "456e7890-e89b-12d3-a456-************",
        "quantity": 2,
        "price": 29.99,
        "title": "Sample Product"
      }
    ],
    "currency": "USD"
  }'
```

### List Orders with Filters

```bash
curl "http://localhost:8080/api/v1/orders?status=pending&limit=10&sort_by=created_at" \
  -H "Authorization: Bearer your-token"
```

### Update Order Status

```bash
curl -X PATCH http://localhost:8080/api/v1/orders/{order-id}/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "status": "confirmed",
    "notes": "Order confirmed"
  }'
```
