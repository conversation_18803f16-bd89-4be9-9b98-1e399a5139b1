package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"order-service/internal/config"
	"order-service/internal/handlers"
	"order-service/internal/repositories"
	"order-service/internal/router"
	"order-service/internal/services"
	"order-service/pkg/database"
	"order-service/pkg/logger"
	"order-service/pkg/validator"

	"go.uber.org/zap"
)

// @title Order Management Service API
// @version 1.0
// @description A comprehensive order management microservice for ecommerce applications
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("Failed to load configuration: %v", err))
	}

	// Initialize logger
	log, err := logger.New(&cfg.Logger)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize logger: %v", err))
	}
	defer log.Sync()

	log.Info("Starting Order Management Service",
		zap.String("version", "1.0.0"),
		zap.String("environment", cfg.App.Environment),
		zap.Int("port", cfg.Server.Port))

	// Initialize database
	db, err := database.New(&cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database", zap.Error(err))
	}

	// Run database migrations
	if err := db.RunMigrations("file://migrations"); err != nil {
		log.Fatal("Failed to run database migrations", zap.Error(err))
	}

	// Seed database if in development mode
	if cfg.App.Environment == "development" {
		if err := db.SeedData(); err != nil {
			log.Warn("Failed to seed database", zap.Error(err))
		}
	}

	// Initialize validator
	validator.SetupGinValidator()

	// Initialize repositories
	orderRepo := repositories.NewOrderRepository(db.DB)

	// Initialize services
	orderService := services.NewOrderService(orderRepo, log)

	// Initialize handlers
	orderHandler := handlers.NewOrderHandler(orderService, log)
	healthHandler := handlers.NewHealthHandler(db, log)

	// Initialize router
	appRouter := router.NewRouter(orderHandler, healthHandler, log)
	engine := appRouter.SetupRoutes()

	// Setup additional routes based on environment
	if cfg.App.Environment == "development" {
		appRouter.SetupDevelopmentRoutes(engine)
	}

	// Setup metrics routes
	appRouter.SetupMetricsRoutes(engine)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      engine,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Info("Server starting", zap.String("address", server.Addr))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Server shutting down...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Error("Server forced to shutdown", zap.Error(err))
		return
	}

	// Close database connection
	if sqlDB, err := db.DB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			log.Error("Failed to close database connection", zap.Error(err))
		}
	}

	log.Info("Server exited")
}

// Health check function for container orchestration
func healthCheck() error {
	// This could be used by container orchestration systems
	// to check if the service is healthy
	resp, err := http.Get("http://localhost:8080/health")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}

	return nil
}

// Version information
var (
	Version   = "1.0.0"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

// printVersion prints version information
func printVersion() {
	fmt.Printf("Order Management Service\n")
	fmt.Printf("Version: %s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Printf("Git Commit: %s\n", GitCommit)
}

// Environment-specific initialization
func initializeEnvironment(cfg *config.Config, log *logger.Logger) {
	switch cfg.App.Environment {
	case "development":
		log.Info("Running in development mode")
		// Development-specific initialization
	case "staging":
		log.Info("Running in staging mode")
		// Staging-specific initialization
	case "production":
		log.Info("Running in production mode")
		// Production-specific initialization
		// - Enable metrics collection
		// - Setup monitoring
		// - Configure alerting
	default:
		log.Warn("Unknown environment, defaulting to development mode")
	}
}

// Database health check
func checkDatabaseHealth(db *database.Database) error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// Metrics collection setup
func setupMetrics(cfg *config.Config) {
	if cfg.App.Environment == "production" {
		// Setup Prometheus metrics
		// Setup application metrics
		// Setup custom business metrics
	}
}

// Monitoring and alerting setup
func setupMonitoring(cfg *config.Config, log *logger.Logger) {
	if cfg.App.Environment == "production" {
		// Setup health checks
		// Setup alerting rules
		// Setup log aggregation
		log.Info("Monitoring and alerting configured for production")
	}
}

// Graceful shutdown cleanup
func cleanup(log *logger.Logger) {
	log.Info("Performing cleanup...")

	// Close any open connections
	// Flush any pending operations
	// Save any in-memory state

	log.Info("Cleanup completed")
}

// Signal handling for different operating systems
func setupSignalHandling() chan os.Signal {
	quit := make(chan os.Signal, 1)

	// Handle different signals based on the operating system
	signal.Notify(quit,
		syscall.SIGINT,  // Interrupt from keyboard
		syscall.SIGTERM, // Termination request
		syscall.SIGQUIT, // Quit from keyboard
	)

	return quit
}

// Configuration validation
func validateConfiguration(cfg *config.Config) error {
	if cfg.Server.Port == 0 {
		return fmt.Errorf("server port is required")
	}

	if cfg.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if cfg.App.Environment == "" {
		cfg.App.Environment = "development"
	}

	return nil
}
