# Order Management Service

A comprehensive, production-ready order management microservice built with Go, designed for ecommerce applications. This service provides complete order lifecycle management including creation, status tracking, fulfillment, shipping, and financial processing.

## 🚀 Features

- **Complete Order Management**: Full CRUD operations for orders with comprehensive status tracking
- **Multi-Status Tracking**: Order, fulfillment, shipping, and financial status management with audit trails
- **Clean Architecture**: Well-structured codebase following clean architecture principles
- **RESTful API**: Comprehensive REST API with Swagger documentation
- **Database Migrations**: Automated database schema management
- **Health Checks**: Built-in health monitoring and metrics endpoints
- **Docker Support**: Containerized deployment with docker-compose
- **Production Ready**: Structured logging, middleware, error handling, and monitoring

## 🏗️ Architecture

```
order-service/
├── cmd/
│   └── server/           # Application entry point
├── internal/
│   ├── config/          # Configuration management
│   ├── handlers/        # HTTP handlers
│   ├── middleware/      # HTTP middleware
│   ├── models/          # Domain models
│   ├── repositories/    # Data access layer
│   ├── router/          # Route definitions
│   └── services/        # Business logic layer
├── pkg/
│   ├── database/        # Database utilities
│   ├── logger/          # Logging utilities
│   └── validator/       # Validation utilities
├── migrations/          # Database migrations
├── docs/               # API documentation
└── monitoring/         # Monitoring configuration
```

## 🛠️ Technology Stack

- **Language**: Go 1.21+
- **Framework**: Gin (HTTP router)
- **Database**: PostgreSQL with GORM ORM
- **Documentation**: Swagger/OpenAPI
- **Logging**: Zap (structured logging)
- **Configuration**: Viper
- **Migrations**: golang-migrate
- **Containerization**: Docker & Docker Compose
- **Monitoring**: Prometheus & Grafana ready

## 📋 Prerequisites

- Go 1.21 or higher
- PostgreSQL 12+
- Docker & Docker Compose (for containerized deployment)
- Make (optional, for using Makefile commands)

## 🚀 Quick Start

### Using Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd order-service
   ```

2. **Start the services**
   ```bash
   docker-compose up -d
   ```

3. **Verify the service is running**
   ```bash
   curl http://localhost:8080/health
   ```

4. **Access the API documentation**
   Open http://localhost:8080/swagger/index.html in your browser

### Local Development Setup

1. **Install dependencies**
   ```bash
   go mod download
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL**
   ```bash
   docker run --name postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=order_service -p 5432:5432 -d postgres:15-alpine
   ```

4. **Run database migrations**
   ```bash
   make db-migrate-up
   ```

5. **Start the application**
   ```bash
   make run
   ```

## 🔧 Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and modify as needed:

### Key Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `APP_ENV` | Application environment | `development` |
| `SERVER_PORT` | HTTP server port | `8080` |
| `DB_HOST` | Database host | `localhost` |
| `DB_PORT` | Database port | `5432` |
| `DB_NAME` | Database name | `order_service` |
| `DB_USER` | Database user | `postgres` |
| `DB_PASSWORD` | Database password | `password` |
| `LOG_LEVEL` | Logging level | `info` |

## 📊 API Documentation

### Core Endpoints

#### Orders
- `POST /api/v1/orders` - Create a new order
- `GET /api/v1/orders` - List orders with filtering
- `GET /api/v1/orders/{id}` - Get order by ID
- `GET /api/v1/orders/number/{orderNumber}` - Get order by number
- `PUT /api/v1/orders/{id}` - Update order
- `DELETE /api/v1/orders/{id}` - Delete order

#### Status Management
- `PATCH /api/v1/orders/{id}/status` - Update order status
- `PATCH /api/v1/orders/{id}/fulfillment-status` - Update fulfillment status
- `PATCH /api/v1/orders/{id}/shipping-status` - Update shipping status
- `PATCH /api/v1/orders/{id}/financial-status` - Update financial status

#### Order Items
- `POST /api/v1/orders/{id}/items` - Add item to order
- `PUT /api/v1/orders/items/{itemId}` - Update order item
- `DELETE /api/v1/orders/items/{itemId}` - Remove order item

#### Health & Monitoring
- `GET /health` - Comprehensive health check
- `GET /ready` - Readiness probe
- `GET /live` - Liveness probe
- `GET /metrics` - Application metrics

### Interactive API Documentation

Access the full interactive API documentation at:
- **Local**: http://localhost:8080/swagger/index.html
- **Docker**: http://localhost:8080/swagger/index.html

## 🗄️ Database Schema

### Core Tables

- **orders**: Main order information
- **order_items**: Individual line items
- **order_status_history**: Status change audit trail
- **customers**: Customer information
- **products**: Product catalog
- **product_variants**: Product variations

### Status Types

1. **Order Status**: `pending`, `confirmed`, `processing`, `shipped`, `delivered`, `cancelled`
2. **Fulfillment Status**: `unfulfilled`, `partial`, `fulfilled`, `restocked`
3. **Shipping Status**: `unshipped`, `partial`, `shipped`, `delivered`
4. **Financial Status**: `pending`, `authorized`, `paid`, `refunded`, `voided`

## 🔨 Development

### Available Make Commands

```bash
make help              # Show all available commands
make build             # Build the application
make test              # Run tests
make coverage          # Run tests with coverage
make lint              # Run linter
make fmt               # Format code
make dev               # Run in development mode with hot reload
make docker-build      # Build Docker image
make docker-compose-up # Start with docker-compose
make db-migrate-up     # Run database migrations
make db-seed           # Seed database with test data
```

### Running Tests

```bash
# Run all tests
make test

# Run tests with coverage
make coverage

# Run specific test
go test ./internal/services -v
```

### Code Quality

```bash
# Format code
make fmt

# Run linter
make lint

# Run security scan
make security-scan
```

## 🐳 Docker Deployment

### Building the Image

```bash
make docker-build
```

### Using Docker Compose

The included `docker-compose.yml` provides:
- Order service application
- PostgreSQL database
- Redis (optional caching)
- Database migrations
- Adminer (database management)
- Prometheus & Grafana (monitoring)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f order-service

# Stop services
docker-compose down
```

## 📈 Monitoring

### Health Checks

The service provides multiple health check endpoints:

- `/health` - Comprehensive health check including database connectivity
- `/ready` - Kubernetes readiness probe
- `/live` - Kubernetes liveness probe

### Metrics

Application metrics are available at `/metrics` endpoint, including:
- Runtime metrics (memory, goroutines, GC)
- Database connection pool metrics
- HTTP request metrics
- Custom business metrics

### Prometheus Integration

The service is ready for Prometheus monitoring with:
- Metrics endpoint configuration
- Alert rules for common issues
- Grafana dashboard templates

## 🔒 Security

- Input validation on all endpoints
- SQL injection protection via GORM
- CORS configuration
- Security headers middleware
- Rate limiting support
- Authentication middleware ready

## 🚀 Production Deployment

### Environment Setup

1. Set `APP_ENV=production`
2. Configure proper database credentials
3. Set up SSL certificates
4. Configure monitoring and alerting
5. Set up log aggregation

### Kubernetes Deployment

The service is designed to be Kubernetes-ready with:
- Health check endpoints
- Graceful shutdown handling
- Configuration via environment variables
- Stateless design

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation
- Review the monitoring dashboards

## 📚 Additional Documentation

- [API Reference](docs/API.md) - Detailed API documentation
- [Database Schema](docs/DATABASE.md) - Database design and relationships
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment guide
- [Development Guide](docs/DEVELOPMENT.md) - Development setup and guidelines

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and changes.
