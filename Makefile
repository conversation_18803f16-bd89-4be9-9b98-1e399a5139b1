# Order Management Service Makefile

# Variables
APP_NAME := order-service
VERSION := 1.0.0
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# Go related variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := $(GOCMD) fmt

# Binary names
BINARY_NAME := $(APP_NAME)
BINARY_UNIX := $(BINARY_NAME)_unix

# Docker related variables
DOCKER_IMAGE := $(APP_NAME)
DOCKER_TAG := $(VERSION)

# Database related variables
DB_URL := postgres://postgres:password@localhost:5432/order_service?sslmode=disable
MIGRATE_CMD := migrate

.PHONY: all build clean test coverage deps fmt vet lint run dev docker help

# Default target
all: clean deps fmt vet test build

# Build the application
build:
	@echo "Building $(APP_NAME)..."
	$(GOBUILD) $(LDFLAGS) -o bin/$(BINARY_NAME) cmd/server/main.go

# Build for Linux
build-linux:
	@echo "Building $(APP_NAME) for Linux..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/$(BINARY_UNIX) cmd/server/main.go

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf bin/
	rm -rf coverage/

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
coverage:
	@echo "Running tests with coverage..."
	mkdir -p coverage
	$(GOTEST) -v -coverprofile=coverage/coverage.out ./...
	$(GOCMD) tool cover -html=coverage/coverage.out -o coverage/coverage.html
	@echo "Coverage report generated: coverage/coverage.html"

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
fmt:
	@echo "Formatting code..."
	$(GOFMT) ./...

# Run go vet
vet:
	@echo "Running go vet..."
	$(GOCMD) vet ./...

# Run golangci-lint (requires golangci-lint to be installed)
lint:
	@echo "Running golangci-lint..."
	golangci-lint run

# Run the application
run: build
	@echo "Running $(APP_NAME)..."
	./bin/$(BINARY_NAME)

# Run in development mode with hot reload (requires air to be installed)
dev:
	@echo "Running in development mode..."
	air

# Generate Swagger documentation
swagger:
	@echo "Generating Swagger documentation..."
	swag init -g cmd/server/main.go -o docs/

# Database operations
db-create:
	@echo "Creating database..."
	createdb order_service

db-drop:
	@echo "Dropping database..."
	dropdb order_service

db-migrate-up:
	@echo "Running database migrations..."
	$(MIGRATE_CMD) -path migrations -database "$(DB_URL)" up

db-migrate-down:
	@echo "Rolling back database migrations..."
	$(MIGRATE_CMD) -path migrations -database "$(DB_URL)" down

db-migrate-force:
	@echo "Forcing migration version..."
	@read -p "Enter migration version: " version; \
	$(MIGRATE_CMD) -path migrations -database "$(DB_URL)" force $$version

db-seed:
	@echo "Seeding database..."
	$(GOCMD) run scripts/seed.go

# Docker operations
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	docker tag $(DOCKER_IMAGE):$(DOCKER_TAG) $(DOCKER_IMAGE):latest

docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

docker-push:
	@echo "Pushing Docker image..."
	docker push $(DOCKER_IMAGE):$(DOCKER_TAG)
	docker push $(DOCKER_IMAGE):latest

docker-compose-up:
	@echo "Starting services with docker-compose..."
	docker-compose up -d

docker-compose-down:
	@echo "Stopping services with docker-compose..."
	docker-compose down

docker-compose-logs:
	@echo "Showing docker-compose logs..."
	docker-compose logs -f

# Development tools installation
install-tools:
	@echo "Installing development tools..."
	$(GOGET) -u github.com/cosmtrek/air
	$(GOGET) -u github.com/swaggo/swag/cmd/swag
	curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin v1.54.2

# Install migrate tool
install-migrate:
	@echo "Installing migrate tool..."
	curl -L https://github.com/golang-migrate/migrate/releases/download/v4.16.2/migrate.linux-amd64.tar.gz | tar xvz
	sudo mv migrate /usr/local/bin/

# Environment setup
setup-env:
	@echo "Setting up environment..."
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

# Generate test data
generate-test-data:
	@echo "Generating test data..."
	$(GOCMD) run scripts/generate_test_data.go

# Performance testing
perf-test:
	@echo "Running performance tests..."
	$(GOTEST) -bench=. -benchmem ./...

# Security scanning
security-scan:
	@echo "Running security scan..."
	gosec ./...

# Dependency vulnerability check
vuln-check:
	@echo "Checking for vulnerabilities..."
	$(GOCMD) list -json -m all | nancy sleuth

# Code quality metrics
metrics:
	@echo "Generating code quality metrics..."
	gocyclo -over 15 .
	gocognit -over 15 .

# Release preparation
release: clean deps fmt vet test build-linux docker-build
	@echo "Release $(VERSION) prepared"

# Health check
health:
	@echo "Checking service health..."
	curl -f http://localhost:8080/health || exit 1

# Load testing (requires hey to be installed)
load-test:
	@echo "Running load test..."
	hey -n 1000 -c 10 http://localhost:8080/health

# Backup database
db-backup:
	@echo "Backing up database..."
	pg_dump $(DB_URL) > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore database
db-restore:
	@echo "Restoring database..."
	@read -p "Enter backup file path: " file; \
	psql $(DB_URL) < $$file

# Show help
help:
	@echo "Available commands:"
	@echo "  build          - Build the application"
	@echo "  build-linux    - Build for Linux"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  coverage       - Run tests with coverage"
	@echo "  deps           - Download dependencies"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"
	@echo "  lint           - Run golangci-lint"
	@echo "  run            - Run the application"
	@echo "  dev            - Run in development mode"
	@echo "  swagger        - Generate Swagger docs"
	@echo "  db-create      - Create database"
	@echo "  db-drop        - Drop database"
	@echo "  db-migrate-up  - Run migrations"
	@echo "  db-migrate-down- Rollback migrations"
	@echo "  db-seed        - Seed database"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-compose-up - Start with docker-compose"
	@echo "  install-tools  - Install development tools"
	@echo "  setup-env      - Setup environment"
	@echo "  health         - Check service health"
	@echo "  help           - Show this help"
