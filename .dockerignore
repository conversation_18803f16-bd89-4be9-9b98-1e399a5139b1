# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
docs/
*.md

# Development files
.env
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
bin/
build/
dist/
target/
*.exe
*.dll
*.so
*.dylib

# Test files
*_test.go
test/
tests/
coverage/
*.out
*.prof

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.log

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Deployment files
k8s/
kubernetes/
helm/
terraform/

# Backup files
*.bak
*.backup
*.sql

# Cache directories
.cache/
.npm/
.yarn/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
vendor/ (if not using Go modules properly)

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
scripts/local/
local/

# Monitoring and metrics
monitoring/local/
grafana/data/
prometheus/data/
