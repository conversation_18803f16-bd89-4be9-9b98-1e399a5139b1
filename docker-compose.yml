version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: order-service-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: order_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - order-service-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d order_service"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: order-service-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - order-service-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Order Service Application
  order-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: order-service-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Database configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: order_service
      DB_USER: postgres
      DB_PASSWORD: password
      DB_SSL_MODE: disable
      
      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ""
      
      # Application configuration
      APP_ENV: development
      APP_PORT: 8080
      APP_LOG_LEVEL: info
      APP_LOG_FORMAT: json
      
      # Server configuration
      SERVER_READ_TIMEOUT: 30
      SERVER_WRITE_TIMEOUT: 30
      SERVER_IDLE_TIMEOUT: 60
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - order-service-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database Migration Service
  migrate:
    image: migrate/migrate:v4.16.2
    container_name: order-service-migrate
    volumes:
      - ./migrations:/migrations
    command: [
      "-path", "/migrations",
      "-database", "******************************************/order_service?sslmode=disable",
      "up"
    ]
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - order-service-network
    restart: "no"

  # Adminer for database management (development only)
  adminer:
    image: adminer:latest
    container_name: order-service-adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - order-service-network
    profiles:
      - development

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: order-service-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - order-service-network
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: order-service-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - order-service-network
    profiles:
      - monitoring

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: order-service-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - order-service
    networks:
      - order-service-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  order-service-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
