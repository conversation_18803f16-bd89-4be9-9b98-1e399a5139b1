package router

import (
	"order-service/internal/handlers"
	"order-service/internal/middleware"
	"order-service/pkg/logger"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Router holds the router dependencies
type Router struct {
	orderHandler  *handlers.OrderHandler
	healthHandler *handlers.HealthHandler
	logger        *logger.Logger
}

// NewRouter creates a new router instance
func NewRouter(orderHandler *handlers.OrderHandler, healthHandler *handlers.HealthHandler, logger *logger.Logger) *Router {
	return &Router{
		orderHandler:  orderHandler,
		healthHandler: healthHandler,
		logger:        logger,
	}
}

// SetupRoutes configures all the routes
func (r *Router) SetupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	// Create Gin engine
	engine := gin.New()

	// Global middleware
	engine.Use(middleware.RequestIDMiddleware())
	engine.Use(middleware.LoggerMiddleware(r.logger))
	engine.Use(middleware.ErrorHandlerMiddleware(r.logger))
	engine.Use(middleware.CORSMiddleware())
	engine.Use(middleware.SecurityHeadersMiddleware())
	engine.Use(middleware.HealthCheckMiddleware())

	// API v1 routes
	v1 := engine.Group("/api/v1")
	{
		// Apply authentication middleware to protected routes
		v1.Use(middleware.AuthMiddleware())
		v1.Use(middleware.ValidationErrorMiddleware())

		// Order routes
		orders := v1.Group("/orders")
		{
			// CRUD operations
			orders.POST("", r.orderHandler.CreateOrder)
			orders.GET("", r.orderHandler.ListOrders)
			orders.GET("/:id", r.orderHandler.GetOrder)
			orders.GET("/number/:orderNumber", r.orderHandler.GetOrderByNumber)
			orders.PUT("/:id", r.orderHandler.UpdateOrder)
			orders.DELETE("/:id", r.orderHandler.DeleteOrder)

			// Status management
			orders.PATCH("/:id/status", r.orderHandler.UpdateOrderStatus)
			orders.PATCH("/:id/fulfillment-status", r.orderHandler.UpdateFulfillmentStatus)
			orders.PATCH("/:id/shipping-status", r.orderHandler.UpdateShippingStatus)
			orders.PATCH("/:id/financial-status", r.orderHandler.UpdateFinancialStatus)

			// Order items management
			orders.POST("/:id/items", r.orderHandler.AddOrderItem)
		}

		// Order items routes (for direct item operations)
		v1.PUT("/orders/items/:itemId", r.orderHandler.UpdateOrderItem)
		v1.DELETE("/orders/items/:itemId", r.orderHandler.RemoveOrderItem)

		// Analytics and reporting routes
		analytics := v1.Group("/analytics")
		{
			analytics.GET("/orders/statistics", r.getOrderStatistics)
			analytics.GET("/orders/by-customer/:customerId", r.getOrdersByCustomer)
			analytics.GET("/orders/by-status/:status", r.getOrdersByStatus)
			analytics.GET("/orders/by-date-range", r.getOrdersByDateRange)
		}
	}

	// Health check endpoints (public)
	engine.GET("/health", r.healthHandler.Health)
	engine.GET("/ready", r.healthHandler.Ready)
	engine.GET("/live", r.healthHandler.Live)

	// Swagger documentation
	engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	return engine
}

// Analytics handler methods (these would typically be in a separate analytics handler)

func (r *Router) getOrderStatistics(c *gin.Context) {
	// This is a placeholder - in a real implementation, you'd have a dedicated analytics handler
	c.JSON(200, gin.H{
		"message": "Order statistics endpoint - to be implemented",
	})
}

func (r *Router) getOrdersByCustomer(c *gin.Context) {
	// This is a placeholder - in a real implementation, you'd have a dedicated analytics handler
	c.JSON(200, gin.H{
		"message": "Orders by customer endpoint - to be implemented",
	})
}

func (r *Router) getOrdersByStatus(c *gin.Context) {
	// This is a placeholder - in a real implementation, you'd have a dedicated analytics handler
	c.JSON(200, gin.H{
		"message": "Orders by status endpoint - to be implemented",
	})
}

func (r *Router) getOrdersByDateRange(c *gin.Context) {
	// This is a placeholder - in a real implementation, you'd have a dedicated analytics handler
	c.JSON(200, gin.H{
		"message": "Orders by date range endpoint - to be implemented",
	})
}

// SetupDevelopmentRoutes sets up additional routes for development
func (r *Router) SetupDevelopmentRoutes(engine *gin.Engine) {
	// Development-only routes
	dev := engine.Group("/dev")
	{
		// Database seeding endpoint
		dev.POST("/seed", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "Database seeding endpoint - to be implemented",
			})
		})

		// Reset database endpoint
		dev.POST("/reset", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "Database reset endpoint - to be implemented",
			})
		})

		// Generate test data endpoint
		dev.POST("/generate-test-data", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "Generate test data endpoint - to be implemented",
			})
		})
	}
}

// SetupMetricsRoutes sets up metrics and monitoring routes
func (r *Router) SetupMetricsRoutes(engine *gin.Engine) {
	// Metrics endpoints
	metrics := engine.Group("/metrics")
	{
		// Application metrics endpoint
		metrics.GET("", r.healthHandler.Metrics)
		metrics.GET("/app", r.healthHandler.Metrics)
	}
}

// CORS configuration for different environments
func (r *Router) configureCORS(engine *gin.Engine, environment string) {
	switch environment {
	case "development":
		// Allow all origins in development
		engine.Use(middleware.CORSMiddleware())
	case "staging":
		// Restrict to staging domains
		// This would be configured with specific staging URLs
		engine.Use(middleware.CORSMiddleware())
	case "production":
		// Strict CORS for production
		// This would be configured with specific production URLs
		engine.Use(middleware.CORSMiddleware())
	default:
		engine.Use(middleware.CORSMiddleware())
	}
}

// Rate limiting configuration
func (r *Router) configureRateLimit(engine *gin.Engine, environment string) {
	if environment == "production" {
		// Apply rate limiting in production
		engine.Use(middleware.RateLimitMiddleware())
	}
}

// Security configuration
func (r *Router) configureSecurity(engine *gin.Engine, environment string) {
	// Always apply security headers
	engine.Use(middleware.SecurityHeadersMiddleware())

	if environment == "production" {
		// Additional security measures for production
		// This could include things like:
		// - Stricter CSP headers
		// - HSTS headers
		// - Additional security middleware
	}
}

// Graceful shutdown handler
func (r *Router) SetupGracefulShutdown() {
	// This would typically be implemented in the main application
	// to handle graceful shutdown of the HTTP server
}
