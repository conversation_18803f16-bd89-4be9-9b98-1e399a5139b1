package repositories

import (
	"fmt"
	"time"

	"order-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrderRepository defines the interface for order data operations
type OrderRepository interface {
	Create(order *models.Order) error
	GetByID(id uuid.UUID) (*models.Order, error)
	GetByOrderNumber(orderNumber string) (*models.Order, error)
	Update(order *models.Order) error
	Delete(id uuid.UUID) error
	List(filters OrderFilters) ([]*models.Order, int64, error)
	GetOrderItems(orderID uuid.UUID) ([]*models.OrderItem, error)
	AddOrderItem(item *models.OrderItem) error
	UpdateOrderItem(item *models.OrderItem) error
	RemoveOrderItem(id uuid.UUID) error
	GetStatusHistory(orderID uuid.UUID) ([]*models.OrderStatusHistory, error)
	AddStatusHistory(history *models.OrderStatusHistory) error
	UpdateOrderStatus(orderID uuid.UUID, status models.OrderStatus, comment string, changedBy uuid.UUID) error
	UpdateFulfillmentStatus(orderID uuid.UUID, status models.FulfillmentStatus, comment string, changedBy uuid.UUID) error
	UpdateShippingStatus(orderID uuid.UUID, status models.ShippingStatus, comment string, changedBy uuid.UUID) error
	UpdateFinancialStatus(orderID uuid.UUID, status models.FinancialStatus, comment string, changedBy uuid.UUID) error
	GetOrdersByCustomer(customerID uuid.UUID, limit, offset int) ([]*models.Order, error)
	GetOrdersByStatus(status models.OrderStatus, limit, offset int) ([]*models.Order, error)
	GetOrdersByDateRange(startDate, endDate time.Time, limit, offset int) ([]*models.Order, error)
}

// OrderFilters represents filters for listing orders
type OrderFilters struct {
	CustomerID        *uuid.UUID
	CustomerEmail     *string
	Status            *models.OrderStatus
	FulfillmentStatus *models.FulfillmentStatus
	ShippingStatus    *models.ShippingStatus
	FinancialStatus   *models.FinancialStatus
	StartDate         *time.Time
	EndDate           *time.Time
	MinAmount         *float64
	MaxAmount         *float64
	Tags              []string
	Limit             int
	Offset            int
	SortBy            string
	SortOrder         string
}

// orderRepository implements OrderRepository
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository creates a new order repository
func NewOrderRepository(db *gorm.DB) OrderRepository {
	return &orderRepository{db: db}
}

// Create creates a new order
func (r *orderRepository) Create(order *models.Order) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Create the order
		if err := tx.Create(order).Error; err != nil {
			return fmt.Errorf("failed to create order: %w", err)
		}

		// Add initial status history
		statusHistory := &models.OrderStatusHistory{
			OrderID:       order.ID,
			StatusType:    "order",
			NewStatus:     string(order.Status),
			Comment:       "Order created",
			ChangedByType: "system",
		}
		if err := tx.Create(statusHistory).Error; err != nil {
			return fmt.Errorf("failed to create status history: %w", err)
		}

		return nil
	})
}

// GetByID retrieves an order by ID
func (r *orderRepository) GetByID(id uuid.UUID) (*models.Order, error) {
	var order models.Order
	err := r.db.Preload("Items").Preload("StatusHistory").First(&order, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByOrderNumber retrieves an order by order number
func (r *orderRepository) GetByOrderNumber(orderNumber string) (*models.Order, error) {
	var order models.Order
	err := r.db.Preload("Items").Preload("StatusHistory").First(&order, "order_number = ?", orderNumber).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// Update updates an existing order
func (r *orderRepository) Update(order *models.Order) error {
	return r.db.Save(order).Error
}

// Delete soft deletes an order
func (r *orderRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&models.Order{}, "id = ?", id).Error
}

// List retrieves orders with filters
func (r *orderRepository) List(filters OrderFilters) ([]*models.Order, int64, error) {
	var orders []*models.Order
	var total int64

	query := r.db.Model(&models.Order{})

	// Apply filters
	if filters.CustomerID != nil {
		query = query.Where("customer_id = ?", *filters.CustomerID)
	}
	if filters.CustomerEmail != nil {
		query = query.Where("customer_email ILIKE ?", "%"+*filters.CustomerEmail+"%")
	}
	if filters.Status != nil {
		query = query.Where("status = ?", *filters.Status)
	}
	if filters.FulfillmentStatus != nil {
		query = query.Where("fulfillment_status = ?", *filters.FulfillmentStatus)
	}
	if filters.ShippingStatus != nil {
		query = query.Where("shipping_status = ?", *filters.ShippingStatus)
	}
	if filters.FinancialStatus != nil {
		query = query.Where("financial_status = ?", *filters.FinancialStatus)
	}
	if filters.StartDate != nil {
		query = query.Where("created_at >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("created_at <= ?", *filters.EndDate)
	}
	if filters.MinAmount != nil {
		query = query.Where("total_amount >= ?", *filters.MinAmount)
	}
	if filters.MaxAmount != nil {
		query = query.Where("total_amount <= ?", *filters.MaxAmount)
	}
	if len(filters.Tags) > 0 {
		query = query.Where("tags && ?", filters.Tags)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filters.SortBy != "" {
		sortBy = filters.SortBy
	}
	sortOrder := "DESC"
	if filters.SortOrder != "" {
		sortOrder = filters.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filters.Limit > 0 {
		query = query.Limit(filters.Limit)
	}
	if filters.Offset > 0 {
		query = query.Offset(filters.Offset)
	}

	// Execute query with preloads
	err := query.Preload("Items").Find(&orders).Error
	return orders, total, err
}

// GetOrderItems retrieves all items for an order
func (r *orderRepository) GetOrderItems(orderID uuid.UUID) ([]*models.OrderItem, error) {
	var items []*models.OrderItem
	err := r.db.Where("order_id = ?", orderID).Find(&items).Error
	return items, err
}

// AddOrderItem adds an item to an order
func (r *orderRepository) AddOrderItem(item *models.OrderItem) error {
	return r.db.Create(item).Error
}

// UpdateOrderItem updates an order item
func (r *orderRepository) UpdateOrderItem(item *models.OrderItem) error {
	return r.db.Save(item).Error
}

// RemoveOrderItem removes an item from an order
func (r *orderRepository) RemoveOrderItem(id uuid.UUID) error {
	return r.db.Delete(&models.OrderItem{}, "id = ?", id).Error
}

// GetStatusHistory retrieves status history for an order
func (r *orderRepository) GetStatusHistory(orderID uuid.UUID) ([]*models.OrderStatusHistory, error) {
	var history []*models.OrderStatusHistory
	err := r.db.Where("order_id = ?", orderID).Order("created_at DESC").Find(&history).Error
	return history, err
}

// AddStatusHistory adds a status history entry
func (r *orderRepository) AddStatusHistory(history *models.OrderStatusHistory) error {
	return r.db.Create(history).Error
}

// updateStatus is a helper function to update order status and add history
func (r *orderRepository) updateStatus(orderID uuid.UUID, statusType, newStatus, comment string, changedBy uuid.UUID) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Get current order
		var order models.Order
		if err := tx.First(&order, "id = ?", orderID).Error; err != nil {
			return err
		}

		// Get previous status
		var previousStatus string
		switch statusType {
		case "order":
			previousStatus = string(order.Status)
			order.Status = models.OrderStatus(newStatus)
		case "fulfillment":
			previousStatus = string(order.FulfillmentStatus)
			order.FulfillmentStatus = models.FulfillmentStatus(newStatus)
		case "shipping":
			previousStatus = string(order.ShippingStatus)
			order.ShippingStatus = models.ShippingStatus(newStatus)
		case "financial":
			previousStatus = string(order.FinancialStatus)
			order.FinancialStatus = models.FinancialStatus(newStatus)
		}

		// Update order
		if err := tx.Save(&order).Error; err != nil {
			return err
		}

		// Add status history
		history := &models.OrderStatusHistory{
			OrderID:        orderID,
			StatusType:     statusType,
			PreviousStatus: previousStatus,
			NewStatus:      newStatus,
			Comment:        comment,
			ChangedBy:      changedBy,
			ChangedByType:  "user",
		}
		return tx.Create(history).Error
	})
}

// UpdateOrderStatus updates the order status
func (r *orderRepository) UpdateOrderStatus(orderID uuid.UUID, status models.OrderStatus, comment string, changedBy uuid.UUID) error {
	return r.updateStatus(orderID, "order", string(status), comment, changedBy)
}

// UpdateFulfillmentStatus updates the fulfillment status
func (r *orderRepository) UpdateFulfillmentStatus(orderID uuid.UUID, status models.FulfillmentStatus, comment string, changedBy uuid.UUID) error {
	return r.updateStatus(orderID, "fulfillment", string(status), comment, changedBy)
}

// UpdateShippingStatus updates the shipping status
func (r *orderRepository) UpdateShippingStatus(orderID uuid.UUID, status models.ShippingStatus, comment string, changedBy uuid.UUID) error {
	return r.updateStatus(orderID, "shipping", string(status), comment, changedBy)
}

// UpdateFinancialStatus updates the financial status
func (r *orderRepository) UpdateFinancialStatus(orderID uuid.UUID, status models.FinancialStatus, comment string, changedBy uuid.UUID) error {
	return r.updateStatus(orderID, "financial", string(status), comment, changedBy)
}

// GetOrdersByCustomer retrieves orders for a specific customer
func (r *orderRepository) GetOrdersByCustomer(customerID uuid.UUID, limit, offset int) ([]*models.Order, error) {
	var orders []*models.Order
	query := r.db.Where("customer_id = ?", customerID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.Preload("Items").Find(&orders).Error
	return orders, err
}

// GetOrdersByStatus retrieves orders by status
func (r *orderRepository) GetOrdersByStatus(status models.OrderStatus, limit, offset int) ([]*models.Order, error) {
	var orders []*models.Order
	query := r.db.Where("status = ?", status).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.Preload("Items").Find(&orders).Error
	return orders, err
}

// GetOrdersByDateRange retrieves orders within a date range
func (r *orderRepository) GetOrdersByDateRange(startDate, endDate time.Time, limit, offset int) ([]*models.Order, error) {
	var orders []*models.Order
	query := r.db.Where("created_at BETWEEN ? AND ?", startDate, endDate).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.Preload("Items").Find(&orders).Error
	return orders, err
}
