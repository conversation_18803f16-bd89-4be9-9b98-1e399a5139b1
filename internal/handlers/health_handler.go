package handlers

import (
	"net/http"
	"runtime"
	"time"

	"order-service/pkg/database"
	"order-service/pkg/logger"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check and monitoring endpoints
type HealthHandler struct {
	db     *database.Database
	logger *logger.Logger
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *database.Database, logger *logger.Logger) *HealthHandler {
	return &HealthHandler{
		db:     db,
		logger: logger,
	}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Service   string                 `json:"service"`
	Version   string                 `json:"version"`
	Uptime    string                 `json:"uptime"`
	Checks    map[string]HealthCheck `json:"checks"`
}

// HealthCheck represents an individual health check
type HealthCheck struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
	Latency string `json:"latency,omitempty"`
}

// MetricsResponse represents the metrics response
type MetricsResponse struct {
	Timestamp time.Time              `json:"timestamp"`
	Service   string                 `json:"service"`
	Runtime   RuntimeMetrics         `json:"runtime"`
	Database  DatabaseMetrics        `json:"database"`
	HTTP      HTTPMetrics            `json:"http"`
	Custom    map[string]interface{} `json:"custom"`
}

// RuntimeMetrics represents Go runtime metrics
type RuntimeMetrics struct {
	GoVersion     string `json:"go_version"`
	NumGoroutines int    `json:"num_goroutines"`
	NumCPU        int    `json:"num_cpu"`
	MemoryAlloc   uint64 `json:"memory_alloc"`
	MemoryTotal   uint64 `json:"memory_total"`
	MemorySys     uint64 `json:"memory_sys"`
	GCRuns        uint32 `json:"gc_runs"`
	LastGCTime    string `json:"last_gc_time"`
	NextGCTarget  uint64 `json:"next_gc_target"`
}

// DatabaseMetrics represents database metrics
type DatabaseMetrics struct {
	Status            string `json:"status"`
	OpenConnections   int    `json:"open_connections"`
	InUse             int    `json:"in_use"`
	Idle              int    `json:"idle"`
	WaitCount         int64  `json:"wait_count"`
	WaitDuration      string `json:"wait_duration"`
	MaxIdleClosed     int64  `json:"max_idle_closed"`
	MaxLifetimeClosed int64  `json:"max_lifetime_closed"`
}

// HTTPMetrics represents HTTP metrics
type HTTPMetrics struct {
	RequestsTotal   int64  `json:"requests_total"`
	RequestsSuccess int64  `json:"requests_success"`
	RequestsError   int64  `json:"requests_error"`
	AverageLatency  string `json:"average_latency"`
}

var (
	startTime       = time.Now()
	requestsTotal   int64
	requestsSuccess int64
	requestsError   int64
)

// Health performs a comprehensive health check
// @Summary Health check endpoint
// @Description Comprehensive health check including database connectivity
// @Tags monitoring
// @Produce json
// @Success 200 {object} HealthResponse
// @Failure 503 {object} HealthResponse
// @Router /health [get]
func (h *HealthHandler) Health(c *gin.Context) {
	checks := make(map[string]HealthCheck)
	overallStatus := "healthy"

	// Database health check
	dbCheck := h.checkDatabase()
	checks["database"] = dbCheck
	if dbCheck.Status != "healthy" {
		overallStatus = "unhealthy"
	}

	// Memory health check
	memCheck := h.checkMemory()
	checks["memory"] = memCheck
	if memCheck.Status != "healthy" {
		overallStatus = "degraded"
	}

	// Disk space check (simplified)
	diskCheck := h.checkDiskSpace()
	checks["disk"] = diskCheck
	if diskCheck.Status != "healthy" {
		overallStatus = "degraded"
	}

	response := HealthResponse{
		Status:    overallStatus,
		Timestamp: time.Now().UTC(),
		Service:   "order-service",
		Version:   "1.0.0",
		Uptime:    time.Since(startTime).String(),
		Checks:    checks,
	}

	statusCode := http.StatusOK
	if overallStatus == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, response)
}

// Ready checks if the service is ready to accept requests
// @Summary Readiness check endpoint
// @Description Check if service is ready to accept requests
// @Tags monitoring
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 503 {object} map[string]string
// @Router /ready [get]
func (h *HealthHandler) Ready(c *gin.Context) {
	// Check critical dependencies
	dbCheck := h.checkDatabase()

	if dbCheck.Status != "healthy" {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "not ready",
			"reason": "database not available",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
	})
}

// Live checks if the service is alive
// @Summary Liveness check endpoint
// @Description Check if service is alive
// @Tags monitoring
// @Produce json
// @Success 200 {object} map[string]string
// @Router /live [get]
func (h *HealthHandler) Live(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"timestamp": time.Now().UTC(),
		"uptime":    time.Since(startTime).String(),
	})
}

// Metrics returns application metrics
// @Summary Application metrics endpoint
// @Description Get application performance metrics
// @Tags monitoring
// @Produce json
// @Success 200 {object} MetricsResponse
// @Router /metrics [get]
func (h *HealthHandler) Metrics(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	runtimeMetrics := RuntimeMetrics{
		GoVersion:     runtime.Version(),
		NumGoroutines: runtime.NumGoroutine(),
		NumCPU:        runtime.NumCPU(),
		MemoryAlloc:   m.Alloc,
		MemoryTotal:   m.TotalAlloc,
		MemorySys:     m.Sys,
		GCRuns:        m.NumGC,
		LastGCTime:    time.Unix(0, int64(m.LastGC)).Format(time.RFC3339),
		NextGCTarget:  m.NextGC,
	}

	dbMetrics := h.getDatabaseMetrics()
	httpMetrics := h.getHTTPMetrics()

	response := MetricsResponse{
		Timestamp: time.Now().UTC(),
		Service:   "order-service",
		Runtime:   runtimeMetrics,
		Database:  dbMetrics,
		HTTP:      httpMetrics,
		Custom: map[string]interface{}{
			"uptime_seconds": time.Since(startTime).Seconds(),
			"start_time":     startTime.Format(time.RFC3339),
		},
	}

	c.JSON(http.StatusOK, response)
}

// checkDatabase performs database health check
func (h *HealthHandler) checkDatabase() HealthCheck {
	start := time.Now()

	sqlDB, err := h.db.DB.DB()
	if err != nil {
		return HealthCheck{
			Status:  "unhealthy",
			Message: "Failed to get database instance: " + err.Error(),
			Latency: time.Since(start).String(),
		}
	}

	if err := sqlDB.Ping(); err != nil {
		return HealthCheck{
			Status:  "unhealthy",
			Message: "Database ping failed: " + err.Error(),
			Latency: time.Since(start).String(),
		}
	}

	return HealthCheck{
		Status:  "healthy",
		Message: "Database connection successful",
		Latency: time.Since(start).String(),
	}
}

// checkMemory performs memory health check
func (h *HealthHandler) checkMemory() HealthCheck {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Check if memory usage is too high (example: > 1GB)
	const maxMemory = 1024 * 1024 * 1024 // 1GB

	if m.Alloc > maxMemory {
		return HealthCheck{
			Status:  "unhealthy",
			Message: "High memory usage detected",
		}
	}

	return HealthCheck{
		Status:  "healthy",
		Message: "Memory usage normal",
	}
}

// checkDiskSpace performs disk space health check (simplified)
func (h *HealthHandler) checkDiskSpace() HealthCheck {
	// This is a simplified check - in production you'd want to check actual disk space
	return HealthCheck{
		Status:  "healthy",
		Message: "Disk space sufficient",
	}
}

// getDatabaseMetrics returns database metrics
func (h *HealthHandler) getDatabaseMetrics() DatabaseMetrics {
	sqlDB, err := h.db.DB.DB()
	if err != nil {
		return DatabaseMetrics{
			Status: "error",
		}
	}

	stats := sqlDB.Stats()

	return DatabaseMetrics{
		Status:            "healthy",
		OpenConnections:   stats.OpenConnections,
		InUse:             stats.InUse,
		Idle:              stats.Idle,
		WaitCount:         stats.WaitCount,
		WaitDuration:      stats.WaitDuration.String(),
		MaxIdleClosed:     stats.MaxIdleClosed,
		MaxLifetimeClosed: stats.MaxLifetimeClosed,
	}
}

// getHTTPMetrics returns HTTP metrics
func (h *HealthHandler) getHTTPMetrics() HTTPMetrics {
	var avgLatency string
	if requestsTotal > 0 {
		avgLatency = "0ms" // This would be calculated from actual request tracking
	}

	return HTTPMetrics{
		RequestsTotal:   requestsTotal,
		RequestsSuccess: requestsSuccess,
		RequestsError:   requestsError,
		AverageLatency:  avgLatency,
	}
}

// IncrementRequestMetrics increments request metrics (to be called from middleware)
func IncrementRequestMetrics(success bool) {
	requestsTotal++
	if success {
		requestsSuccess++
	} else {
		requestsError++
	}
}
