package handlers

import (
	"net/http"
	"strconv"
	"time"

	"order-service/internal/models"
	"order-service/internal/repositories"
	"order-service/internal/services"
	"order-service/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// OrderHandler handles order-related HTTP requests
type OrderHandler struct {
	orderService services.OrderService
	logger       *logger.Logger
}

// NewOrderHandler creates a new order handler
func NewOrderHandler(orderService services.OrderService, logger *logger.Logger) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
	}
}

// CreateOrder creates a new order
// @Summary Create a new order
// @Description Create a new order with items
// @Tags orders
// @Accept json
// @Produce json
// @Param order body services.CreateOrderRequest true "Order data"
// @Success 201 {object} models.Order
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var req services.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	order, err := h.orderService.CreateOrder(&req)
	if err != nil {
		h.logger.Error("Failed to create order", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to create order",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// GetOrder retrieves an order by ID
// @Summary Get an order by ID
// @Description Get an order by its UUID
// @Tags orders
// @Produce json
// @Param id path string true "Order ID"
// @Success 200 {object} models.Order
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id} [get]
func (h *OrderHandler) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	order, err := h.orderService.GetOrder(id)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to get order", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to get order",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetOrderByNumber retrieves an order by order number
// @Summary Get an order by order number
// @Description Get an order by its order number
// @Tags orders
// @Produce json
// @Param orderNumber path string true "Order Number"
// @Success 200 {object} models.Order
// @Failure 404 {object} ErrorResponse
// @Router /orders/number/{orderNumber} [get]
func (h *OrderHandler) GetOrderByNumber(c *gin.Context) {
	orderNumber := c.Param("orderNumber")

	order, err := h.orderService.GetOrderByNumber(orderNumber)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to get order by number", zap.String("order_number", orderNumber), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to get order",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, order)
}

// UpdateOrder updates an existing order
// @Summary Update an order
// @Description Update an existing order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param order body services.UpdateOrderRequest true "Order update data"
// @Success 200 {object} models.Order
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id} [put]
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req services.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	order, err := h.orderService.UpdateOrder(id, &req)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update order", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update order",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, order)
}

// DeleteOrder deletes an order
// @Summary Delete an order
// @Description Delete an order by ID
// @Tags orders
// @Param id path string true "Order ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id} [delete]
func (h *OrderHandler) DeleteOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	err = h.orderService.DeleteOrder(id)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to delete order", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to delete order",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListOrders lists orders with filters
// @Summary List orders
// @Description List orders with optional filters
// @Tags orders
// @Produce json
// @Param customer_id query string false "Customer ID"
// @Param customer_email query string false "Customer Email"
// @Param status query string false "Order Status"
// @Param fulfillment_status query string false "Fulfillment Status"
// @Param shipping_status query string false "Shipping Status"
// @Param financial_status query string false "Financial Status"
// @Param start_date query string false "Start Date (RFC3339)"
// @Param end_date query string false "End Date (RFC3339)"
// @Param min_amount query number false "Minimum Amount"
// @Param max_amount query number false "Maximum Amount"
// @Param limit query int false "Limit" default(20)
// @Param offset query int false "Offset" default(0)
// @Param sort_by query string false "Sort By" default(created_at)
// @Param sort_order query string false "Sort Order" default(DESC)
// @Success 200 {object} ListOrdersResponse
// @Failure 400 {object} ErrorResponse
// @Router /orders [get]
func (h *OrderHandler) ListOrders(c *gin.Context) {
	filters := repositories.OrderFilters{
		Limit:     20,
		Offset:    0,
		SortBy:    "created_at",
		SortOrder: "DESC",
	}

	// Parse query parameters
	if customerID := c.Query("customer_id"); customerID != "" {
		if id, err := uuid.Parse(customerID); err == nil {
			filters.CustomerID = &id
		}
	}

	if customerEmail := c.Query("customer_email"); customerEmail != "" {
		filters.CustomerEmail = &customerEmail
	}

	if status := c.Query("status"); status != "" {
		orderStatus := models.OrderStatus(status)
		filters.Status = &orderStatus
	}

	if fulfillmentStatus := c.Query("fulfillment_status"); fulfillmentStatus != "" {
		fStatus := models.FulfillmentStatus(fulfillmentStatus)
		filters.FulfillmentStatus = &fStatus
	}

	if shippingStatus := c.Query("shipping_status"); shippingStatus != "" {
		sStatus := models.ShippingStatus(shippingStatus)
		filters.ShippingStatus = &sStatus
	}

	if financialStatus := c.Query("financial_status"); financialStatus != "" {
		fStatus := models.FinancialStatus(financialStatus)
		filters.FinancialStatus = &fStatus
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if date, err := time.Parse(time.RFC3339, startDate); err == nil {
			filters.StartDate = &date
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if date, err := time.Parse(time.RFC3339, endDate); err == nil {
			filters.EndDate = &date
		}
	}

	if minAmount := c.Query("min_amount"); minAmount != "" {
		if amount, err := strconv.ParseFloat(minAmount, 64); err == nil {
			filters.MinAmount = &amount
		}
	}

	if maxAmount := c.Query("max_amount"); maxAmount != "" {
		if amount, err := strconv.ParseFloat(maxAmount, 64); err == nil {
			filters.MaxAmount = &amount
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 {
			filters.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil && o >= 0 {
			filters.Offset = o
		}
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		filters.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filters.SortOrder = sortOrder
	}

	orders, total, err := h.orderService.ListOrders(filters)
	if err != nil {
		h.logger.Error("Failed to list orders", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to list orders",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	response := ListOrdersResponse{
		Orders: orders,
		Total:  total,
		Limit:  filters.Limit,
		Offset: filters.Offset,
	}

	c.JSON(http.StatusOK, response)
}

// Response types
type ErrorResponse struct {
	Error     string `json:"error"`
	Details   string `json:"details,omitempty"`
	RequestID string `json:"request_id"`
}

type ListOrdersResponse struct {
	Orders []*models.Order `json:"orders"`
	Total  int64           `json:"total"`
	Limit  int             `json:"limit"`
	Offset int             `json:"offset"`
}

type StatusUpdateRequest struct {
	Status    string `json:"status" validate:"required"`
	Comment   string `json:"comment"`
	ChangedBy string `json:"changed_by"`
}

// Helper functions
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

func getUserID(c *gin.Context) uuid.UUID {
	if userID, exists := c.Get("user_id"); exists {
		if id, err := uuid.Parse(userID.(string)); err == nil {
			return id
		}
	}
	return uuid.Nil
}

// Status update handlers

// UpdateOrderStatus updates the order status
// @Summary Update order status
// @Description Update the status of an order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param status body StatusUpdateRequest true "Status update data"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id}/status [patch]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	status := models.OrderStatus(req.Status)
	changedBy := getUserID(c)

	err = h.orderService.UpdateOrderStatus(id, status, req.Comment, changedBy)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update order status", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update order status",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Order status updated successfully",
		"status":  req.Status,
	})
}

// UpdateFulfillmentStatus updates the fulfillment status
// @Summary Update fulfillment status
// @Description Update the fulfillment status of an order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param status body StatusUpdateRequest true "Status update data"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id}/fulfillment-status [patch]
func (h *OrderHandler) UpdateFulfillmentStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	status := models.FulfillmentStatus(req.Status)
	changedBy := getUserID(c)

	err = h.orderService.UpdateFulfillmentStatus(id, status, req.Comment, changedBy)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update fulfillment status", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update fulfillment status",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Fulfillment status updated successfully",
		"status":  req.Status,
	})
}

// UpdateShippingStatus updates the shipping status
// @Summary Update shipping status
// @Description Update the shipping status of an order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param status body StatusUpdateRequest true "Status update data"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id}/shipping-status [patch]
func (h *OrderHandler) UpdateShippingStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	status := models.ShippingStatus(req.Status)
	changedBy := getUserID(c)

	err = h.orderService.UpdateShippingStatus(id, status, req.Comment, changedBy)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update shipping status", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update shipping status",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Shipping status updated successfully",
		"status":  req.Status,
	})
}

// UpdateFinancialStatus updates the financial status
// @Summary Update financial status
// @Description Update the financial status of an order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param status body StatusUpdateRequest true "Status update data"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id}/financial-status [patch]
func (h *OrderHandler) UpdateFinancialStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	status := models.FinancialStatus(req.Status)
	changedBy := getUserID(c)

	err = h.orderService.UpdateFinancialStatus(id, status, req.Comment, changedBy)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update financial status", zap.String("order_id", id.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update financial status",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Financial status updated successfully",
		"status":  req.Status,
	})
}

// Order Items handlers

// AddOrderItem adds an item to an order
// @Summary Add item to order
// @Description Add a new item to an existing order
// @Tags orders
// @Accept json
// @Produce json
// @Param id path string true "Order ID"
// @Param item body services.AddOrderItemRequest true "Order item data"
// @Success 201 {object} models.OrderItem
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/{id}/items [post]
func (h *OrderHandler) AddOrderItem(c *gin.Context) {
	idStr := c.Param("id")
	orderID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid order ID",
			Details:   "Order ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req services.AddOrderItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	item, err := h.orderService.AddOrderItem(orderID, &req)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to add order item", zap.String("order_id", orderID.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to add order item",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateOrderItem updates an order item
// @Summary Update order item
// @Description Update an existing order item
// @Tags orders
// @Accept json
// @Produce json
// @Param itemId path string true "Order Item ID"
// @Param item body services.UpdateOrderItemRequest true "Order item update data"
// @Success 200 {object} models.OrderItem
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/items/{itemId} [put]
func (h *OrderHandler) UpdateOrderItem(c *gin.Context) {
	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid item ID",
			Details:   "Item ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	var req services.UpdateOrderItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid request body",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	item, err := h.orderService.UpdateOrderItem(itemID, &req)
	if err != nil {
		if err.Error() == "record not found" || err.Error() == "order item not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order item not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to update order item", zap.String("item_id", itemID.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to update order item",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.JSON(http.StatusOK, item)
}

// RemoveOrderItem removes an item from an order
// @Summary Remove order item
// @Description Remove an item from an order
// @Tags orders
// @Param itemId path string true "Order Item ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /orders/items/{itemId} [delete]
func (h *OrderHandler) RemoveOrderItem(c *gin.Context) {
	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:     "Invalid item ID",
			Details:   "Item ID must be a valid UUID",
			RequestID: getRequestID(c),
		})
		return
	}

	err = h.orderService.RemoveOrderItem(itemID)
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:     "Order item not found",
				RequestID: getRequestID(c),
			})
			return
		}
		h.logger.Error("Failed to remove order item", zap.String("item_id", itemID.String()), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:     "Failed to remove order item",
			Details:   err.Error(),
			RequestID: getRequestID(c),
		})
		return
	}

	c.Status(http.StatusNoContent)
}
