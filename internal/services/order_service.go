package services

import (
	"fmt"
	"time"

	"order-service/internal/models"
	"order-service/internal/repositories"
	"order-service/pkg/logger"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// OrderService defines the interface for order business logic
type OrderService interface {
	CreateOrder(req *CreateOrderRequest) (*models.Order, error)
	GetOrder(id uuid.UUID) (*models.Order, error)
	GetOrderByNumber(orderNumber string) (*models.Order, error)
	UpdateOrder(id uuid.UUID, req *UpdateOrderRequest) (*models.Order, error)
	DeleteOrder(id uuid.UUID) error
	ListOrders(filters repositories.OrderFilters) ([]*models.Order, int64, error)

	// Status management
	UpdateOrderStatus(id uuid.UUID, status models.OrderStatus, comment string, changedBy uuid.UUID) error
	UpdateFulfillmentStatus(id uuid.UUID, status models.FulfillmentStatus, comment string, changedBy uuid.UUID) error
	UpdateShippingStatus(id uuid.UUID, status models.ShippingStatus, comment string, changedBy uuid.UUID) error
	UpdateFinancialStatus(id uuid.UUID, status models.FinancialStatus, comment string, changedBy uuid.UUID) error

	// Order items management
	AddOrderItem(orderID uuid.UUID, req *AddOrderItemRequest) (*models.OrderItem, error)
	UpdateOrderItem(itemID uuid.UUID, req *UpdateOrderItemRequest) (*models.OrderItem, error)
	RemoveOrderItem(itemID uuid.UUID) error

	// Analytics and reporting
	GetOrdersByCustomer(customerID uuid.UUID, limit, offset int) ([]*models.Order, error)
	GetOrdersByStatus(status models.OrderStatus, limit, offset int) ([]*models.Order, error)
	GetOrdersByDateRange(startDate, endDate time.Time, limit, offset int) ([]*models.Order, error)
	GetOrderStatistics(startDate, endDate time.Time) (*OrderStatistics, error)
}

// CreateOrderRequest represents the request to create an order
type CreateOrderRequest struct {
	CustomerID      uuid.UUID                `json:"customer_id" validate:"required"`
	CustomerEmail   string                   `json:"customer_email" validate:"required,email"`
	CustomerPhone   string                   `json:"customer_phone"`
	BillingAddress  models.Address           `json:"billing_address" validate:"required"`
	ShippingAddress models.Address           `json:"shipping_address" validate:"required"`
	Items           []CreateOrderItemRequest `json:"items" validate:"required,min=1"`
	Currency        string                   `json:"currency" validate:"required,len=3"`
	TaxAmount       float64                  `json:"tax_amount"`
	ShippingAmount  float64                  `json:"shipping_amount"`
	DiscountAmount  float64                  `json:"discount_amount"`
	Notes           string                   `json:"notes"`
	Tags            []string                 `json:"tags"`
	Metadata        map[string]interface{}   `json:"metadata"`
}

// CreateOrderItemRequest represents an item in the create order request
type CreateOrderItemRequest struct {
	ProductID  uuid.UUID              `json:"product_id" validate:"required"`
	VariantID  *uuid.UUID             `json:"variant_id"`
	Quantity   int                    `json:"quantity" validate:"required,min=1"`
	Price      float64                `json:"price" validate:"required,min=0"`
	Properties map[string]interface{} `json:"properties"`
}

// UpdateOrderRequest represents the request to update an order
type UpdateOrderRequest struct {
	CustomerEmail   *string                `json:"customer_email,omitempty" validate:"omitempty,email"`
	CustomerPhone   *string                `json:"customer_phone,omitempty"`
	BillingAddress  *models.Address        `json:"billing_address,omitempty"`
	ShippingAddress *models.Address        `json:"shipping_address,omitempty"`
	TaxAmount       *float64               `json:"tax_amount,omitempty"`
	ShippingAmount  *float64               `json:"shipping_amount,omitempty"`
	DiscountAmount  *float64               `json:"discount_amount,omitempty"`
	Notes           *string                `json:"notes,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// AddOrderItemRequest represents the request to add an item to an order
type AddOrderItemRequest struct {
	ProductID  uuid.UUID              `json:"product_id" validate:"required"`
	VariantID  *uuid.UUID             `json:"variant_id"`
	Quantity   int                    `json:"quantity" validate:"required,min=1"`
	Price      float64                `json:"price" validate:"required,min=0"`
	Properties map[string]interface{} `json:"properties"`
}

// UpdateOrderItemRequest represents the request to update an order item
type UpdateOrderItemRequest struct {
	Quantity   *int                   `json:"quantity,omitempty" validate:"omitempty,min=1"`
	Price      *float64               `json:"price,omitempty" validate:"omitempty,min=0"`
	Properties map[string]interface{} `json:"properties,omitempty"`
}

// OrderStatistics represents order analytics data
type OrderStatistics struct {
	TotalOrders       int64            `json:"total_orders"`
	TotalRevenue      float64          `json:"total_revenue"`
	AverageOrderValue float64          `json:"average_order_value"`
	StatusBreakdown   map[string]int64 `json:"status_breakdown"`
	TopProducts       []ProductSales   `json:"top_products"`
}

// ProductSales represents product sales data
type ProductSales struct {
	ProductID    uuid.UUID `json:"product_id"`
	ProductTitle string    `json:"product_title"`
	Quantity     int       `json:"quantity"`
	Revenue      float64   `json:"revenue"`
}

// orderService implements OrderService
type orderService struct {
	orderRepo repositories.OrderRepository
	logger    *logger.Logger
}

// NewOrderService creates a new order service
func NewOrderService(orderRepo repositories.OrderRepository, logger *logger.Logger) OrderService {
	return &orderService{
		orderRepo: orderRepo,
		logger:    logger,
	}
}

// CreateOrder creates a new order
func (s *orderService) CreateOrder(req *CreateOrderRequest) (*models.Order, error) {
	s.logger.Info("Creating new order", zap.String("customer_email", req.CustomerEmail))

	// Calculate subtotal from items
	var subtotal float64
	var orderItems []models.OrderItem

	for _, itemReq := range req.Items {
		itemTotal := itemReq.Price * float64(itemReq.Quantity)
		subtotal += itemTotal

		orderItem := models.OrderItem{
			ProductID:  itemReq.ProductID,
			VariantID:  itemReq.VariantID,
			Quantity:   itemReq.Quantity,
			Price:      itemReq.Price,
			TotalPrice: itemTotal,
			Properties: itemReq.Properties,
		}
		orderItems = append(orderItems, orderItem)
	}

	// Create order
	order := &models.Order{
		CustomerID:        req.CustomerID,
		CustomerEmail:     req.CustomerEmail,
		CustomerPhone:     req.CustomerPhone,
		Status:            models.OrderStatusPending,
		FulfillmentStatus: models.FulfillmentStatusPending,
		ShippingStatus:    models.ShippingStatusPending,
		FinancialStatus:   models.FinancialStatusPending,
		SubtotalAmount:    subtotal,
		TaxAmount:         req.TaxAmount,
		ShippingAmount:    req.ShippingAmount,
		DiscountAmount:    req.DiscountAmount,
		Currency:          req.Currency,
		BillingAddress:    req.BillingAddress,
		ShippingAddress:   req.ShippingAddress,
		Items:             orderItems,
		Notes:             req.Notes,
		Tags:              req.Tags,
		Metadata:          req.Metadata,
	}

	// Calculate total
	order.CalculateTotal()

	// Create order in database
	if err := s.orderRepo.Create(order); err != nil {
		s.logger.Error("Failed to create order", zap.Error(err))
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	s.logger.Info("Order created successfully",
		zap.String("order_id", order.ID.String()),
		zap.String("order_number", order.OrderNumber))

	return order, nil
}

// GetOrder retrieves an order by ID
func (s *orderService) GetOrder(id uuid.UUID) (*models.Order, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		s.logger.Error("Failed to get order", zap.String("order_id", id.String()), zap.Error(err))
		return nil, fmt.Errorf("failed to get order: %w", err)
	}
	return order, nil
}

// GetOrderByNumber retrieves an order by order number
func (s *orderService) GetOrderByNumber(orderNumber string) (*models.Order, error) {
	order, err := s.orderRepo.GetByOrderNumber(orderNumber)
	if err != nil {
		s.logger.Error("Failed to get order by number", zap.String("order_number", orderNumber), zap.Error(err))
		return nil, fmt.Errorf("failed to get order: %w", err)
	}
	return order, nil
}

// UpdateOrder updates an existing order
func (s *orderService) UpdateOrder(id uuid.UUID, req *UpdateOrderRequest) (*models.Order, error) {
	// Get existing order
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Update fields if provided
	if req.CustomerEmail != nil {
		order.CustomerEmail = *req.CustomerEmail
	}
	if req.CustomerPhone != nil {
		order.CustomerPhone = *req.CustomerPhone
	}
	if req.BillingAddress != nil {
		order.BillingAddress = *req.BillingAddress
	}
	if req.ShippingAddress != nil {
		order.ShippingAddress = *req.ShippingAddress
	}
	if req.TaxAmount != nil {
		order.TaxAmount = *req.TaxAmount
	}
	if req.ShippingAmount != nil {
		order.ShippingAmount = *req.ShippingAmount
	}
	if req.DiscountAmount != nil {
		order.DiscountAmount = *req.DiscountAmount
	}
	if req.Notes != nil {
		order.Notes = *req.Notes
	}
	if req.Tags != nil {
		order.Tags = req.Tags
	}
	if req.Metadata != nil {
		order.Metadata = req.Metadata
	}

	// Recalculate total
	order.CalculateTotal()

	// Update in database
	if err := s.orderRepo.Update(order); err != nil {
		s.logger.Error("Failed to update order", zap.String("order_id", id.String()), zap.Error(err))
		return nil, fmt.Errorf("failed to update order: %w", err)
	}

	s.logger.Info("Order updated successfully", zap.String("order_id", id.String()))
	return order, nil
}

// DeleteOrder deletes an order
func (s *orderService) DeleteOrder(id uuid.UUID) error {
	if err := s.orderRepo.Delete(id); err != nil {
		s.logger.Error("Failed to delete order", zap.String("order_id", id.String()), zap.Error(err))
		return fmt.Errorf("failed to delete order: %w", err)
	}

	s.logger.Info("Order deleted successfully", zap.String("order_id", id.String()))
	return nil
}

// ListOrders lists orders with filters
func (s *orderService) ListOrders(filters repositories.OrderFilters) ([]*models.Order, int64, error) {
	orders, total, err := s.orderRepo.List(filters)
	if err != nil {
		s.logger.Error("Failed to list orders", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to list orders: %w", err)
	}
	return orders, total, nil
}

// UpdateOrderStatus updates the order status
func (s *orderService) UpdateOrderStatus(id uuid.UUID, status models.OrderStatus, comment string, changedBy uuid.UUID) error {
	if err := s.orderRepo.UpdateOrderStatus(id, status, comment, changedBy); err != nil {
		s.logger.Error("Failed to update order status",
			zap.String("order_id", id.String()),
			zap.String("status", string(status)),
			zap.Error(err))
		return fmt.Errorf("failed to update order status: %w", err)
	}

	s.logger.Info("Order status updated successfully",
		zap.String("order_id", id.String()),
		zap.String("status", string(status)))
	return nil
}

// UpdateFulfillmentStatus updates the fulfillment status
func (s *orderService) UpdateFulfillmentStatus(id uuid.UUID, status models.FulfillmentStatus, comment string, changedBy uuid.UUID) error {
	if err := s.orderRepo.UpdateFulfillmentStatus(id, status, comment, changedBy); err != nil {
		s.logger.Error("Failed to update fulfillment status",
			zap.String("order_id", id.String()),
			zap.String("status", string(status)),
			zap.Error(err))
		return fmt.Errorf("failed to update fulfillment status: %w", err)
	}

	s.logger.Info("Fulfillment status updated successfully",
		zap.String("order_id", id.String()),
		zap.String("status", string(status)))
	return nil
}

// UpdateShippingStatus updates the shipping status
func (s *orderService) UpdateShippingStatus(id uuid.UUID, status models.ShippingStatus, comment string, changedBy uuid.UUID) error {
	if err := s.orderRepo.UpdateShippingStatus(id, status, comment, changedBy); err != nil {
		s.logger.Error("Failed to update shipping status",
			zap.String("order_id", id.String()),
			zap.String("status", string(status)),
			zap.Error(err))
		return fmt.Errorf("failed to update shipping status: %w", err)
	}

	s.logger.Info("Shipping status updated successfully",
		zap.String("order_id", id.String()),
		zap.String("status", string(status)))
	return nil
}

// UpdateFinancialStatus updates the financial status
func (s *orderService) UpdateFinancialStatus(id uuid.UUID, status models.FinancialStatus, comment string, changedBy uuid.UUID) error {
	if err := s.orderRepo.UpdateFinancialStatus(id, status, comment, changedBy); err != nil {
		s.logger.Error("Failed to update financial status",
			zap.String("order_id", id.String()),
			zap.String("status", string(status)),
			zap.Error(err))
		return fmt.Errorf("failed to update financial status: %w", err)
	}

	s.logger.Info("Financial status updated successfully",
		zap.String("order_id", id.String()),
		zap.String("status", string(status)))
	return nil
}

// AddOrderItem adds an item to an order
func (s *orderService) AddOrderItem(orderID uuid.UUID, req *AddOrderItemRequest) (*models.OrderItem, error) {
	// Get the order to validate it exists
	order, err := s.orderRepo.GetByID(orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Create order item
	item := &models.OrderItem{
		OrderID:    orderID,
		ProductID:  req.ProductID,
		Quantity:   req.Quantity,
		Price:      req.Price,
		Properties: req.Properties,
	}
	if req.VariantID != nil {
		item.VariantID = req.VariantID
	}
	item.CalculateItemTotal()

	// Add item to database
	if err := s.orderRepo.AddOrderItem(item); err != nil {
		s.logger.Error("Failed to add order item",
			zap.String("order_id", orderID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to add order item: %w", err)
	}

	// Update order totals
	order.SubtotalAmount += item.TotalPrice
	order.CalculateTotal()
	if err := s.orderRepo.Update(order); err != nil {
		s.logger.Error("Failed to update order totals",
			zap.String("order_id", orderID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to update order totals: %w", err)
	}

	s.logger.Info("Order item added successfully",
		zap.String("order_id", orderID.String()),
		zap.String("item_id", item.ID.String()))
	return item, nil
}

// UpdateOrderItem updates an order item
func (s *orderService) UpdateOrderItem(itemID uuid.UUID, req *UpdateOrderItemRequest) (*models.OrderItem, error) {
	// Get existing item
	items, err := s.orderRepo.GetOrderItems(uuid.Nil) // This needs to be fixed in repository
	if err != nil {
		return nil, fmt.Errorf("failed to get order item: %w", err)
	}

	var item *models.OrderItem
	for _, i := range items {
		if i.ID == itemID {
			item = i
			break
		}
	}

	if item == nil {
		return nil, fmt.Errorf("order item not found")
	}

	oldTotal := item.TotalPrice

	// Update fields if provided
	if req.Quantity != nil {
		item.Quantity = *req.Quantity
	}
	if req.Price != nil {
		item.Price = *req.Price
	}
	if req.Properties != nil {
		item.Properties = req.Properties
	}

	// Recalculate item total
	item.CalculateItemTotal()

	// Update in database
	if err := s.orderRepo.UpdateOrderItem(item); err != nil {
		s.logger.Error("Failed to update order item",
			zap.String("item_id", itemID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to update order item: %w", err)
	}

	// Update order totals
	order, err := s.orderRepo.GetByID(item.OrderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	order.SubtotalAmount = order.SubtotalAmount - oldTotal + item.TotalPrice
	order.CalculateTotal()
	if err := s.orderRepo.Update(order); err != nil {
		s.logger.Error("Failed to update order totals",
			zap.String("order_id", item.OrderID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to update order totals: %w", err)
	}

	s.logger.Info("Order item updated successfully", zap.String("item_id", itemID.String()))
	return item, nil
}

// RemoveOrderItem removes an item from an order
func (s *orderService) RemoveOrderItem(itemID uuid.UUID) error {
	if err := s.orderRepo.RemoveOrderItem(itemID); err != nil {
		s.logger.Error("Failed to remove order item", zap.String("item_id", itemID.String()), zap.Error(err))
		return fmt.Errorf("failed to remove order item: %w", err)
	}

	s.logger.Info("Order item removed successfully", zap.String("item_id", itemID.String()))
	return nil
}

// GetOrdersByCustomer retrieves orders for a specific customer
func (s *orderService) GetOrdersByCustomer(customerID uuid.UUID, limit, offset int) ([]*models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByCustomer(customerID, limit, offset)
	if err != nil {
		s.logger.Error("Failed to get orders by customer",
			zap.String("customer_id", customerID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get orders by customer: %w", err)
	}
	return orders, nil
}

// GetOrdersByStatus retrieves orders by status
func (s *orderService) GetOrdersByStatus(status models.OrderStatus, limit, offset int) ([]*models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByStatus(status, limit, offset)
	if err != nil {
		s.logger.Error("Failed to get orders by status",
			zap.String("status", string(status)),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get orders by status: %w", err)
	}
	return orders, nil
}

// GetOrdersByDateRange retrieves orders within a date range
func (s *orderService) GetOrdersByDateRange(startDate, endDate time.Time, limit, offset int) ([]*models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByDateRange(startDate, endDate, limit, offset)
	if err != nil {
		s.logger.Error("Failed to get orders by date range",
			zap.Time("start_date", startDate),
			zap.Time("end_date", endDate),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get orders by date range: %w", err)
	}
	return orders, nil
}

// GetOrderStatistics retrieves order statistics for a date range
func (s *orderService) GetOrderStatistics(startDate, endDate time.Time) (*OrderStatistics, error) {
	// This is a simplified implementation - in a real system you'd want to use aggregation queries
	filters := repositories.OrderFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		Limit:     0, // Get all orders
	}

	orders, total, err := s.orderRepo.List(filters)
	if err != nil {
		s.logger.Error("Failed to get orders for statistics", zap.Error(err))
		return nil, fmt.Errorf("failed to get orders for statistics: %w", err)
	}

	stats := &OrderStatistics{
		TotalOrders:     total,
		StatusBreakdown: make(map[string]int64),
		TopProducts:     []ProductSales{},
	}

	var totalRevenue float64
	productSales := make(map[uuid.UUID]*ProductSales)

	for _, order := range orders {
		totalRevenue += order.TotalAmount

		// Count status breakdown
		stats.StatusBreakdown[string(order.Status)]++

		// Calculate product sales
		for _, item := range order.Items {
			if existing, ok := productSales[item.ProductID]; ok {
				existing.Quantity += item.Quantity
				existing.Revenue += item.TotalPrice
			} else {
				productSales[item.ProductID] = &ProductSales{
					ProductID:    item.ProductID,
					ProductTitle: item.ProductTitle,
					Quantity:     item.Quantity,
					Revenue:      item.TotalPrice,
				}
			}
		}
	}

	stats.TotalRevenue = totalRevenue
	if total > 0 {
		stats.AverageOrderValue = totalRevenue / float64(total)
	}

	// Convert product sales map to slice (top 10)
	for _, ps := range productSales {
		stats.TopProducts = append(stats.TopProducts, *ps)
	}

	// Sort by revenue (simplified - in production you'd want proper sorting)
	// For now, just return the first 10
	if len(stats.TopProducts) > 10 {
		stats.TopProducts = stats.TopProducts[:10]
	}

	return stats, nil
}
