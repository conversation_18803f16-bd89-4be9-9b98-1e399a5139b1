package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Product represents a product entity
type Product struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title           string    `json:"title" gorm:"not null"`
	Description     string    `json:"description"`
	Handle          string    `json:"handle" gorm:"uniqueIndex"`
	Status          string    `json:"status" gorm:"default:'draft'"` // active, archived, draft
	
	// Product details
	ProductType     string    `json:"product_type"`
	Vendor          string    `json:"vendor"`
	Tags            []string  `json:"tags" gorm:"type:text[]"`
	
	// SEO
	SEOTitle        string    `json:"seo_title"`
	SEODescription  string    `json:"seo_description"`
	
	// Variants
	Variants        []ProductVariant `json:"variants" gorm:"foreignKey:ProductID;constraint:OnDelete:CASCADE"`
	
	// Metadata
	Metadata        map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// ProductVariant represents a product variant
type ProductVariant struct {
	ID                  uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProductID           uuid.UUID `json:"product_id" gorm:"type:uuid;not null;index"`
	Title               string    `json:"title"`
	SKU                 string    `json:"sku" gorm:"uniqueIndex"`
	Barcode             string    `json:"barcode"`
	
	// Pricing
	Price               float64   `json:"price" gorm:"type:decimal(10,2);not null"`
	CompareAtPrice      float64   `json:"compare_at_price" gorm:"type:decimal(10,2)"`
	CostPerItem         float64   `json:"cost_per_item" gorm:"type:decimal(10,2)"`
	
	// Inventory
	InventoryQuantity   int       `json:"inventory_quantity" gorm:"default:0"`
	InventoryPolicy     string    `json:"inventory_policy" gorm:"default:'deny'"` // deny, continue
	InventoryManagement string    `json:"inventory_management"` // shopify, manual, etc.
	
	// Physical properties
	Weight              float64   `json:"weight"`
	WeightUnit          string    `json:"weight_unit" gorm:"default:'kg'"`
	RequiresShipping    bool      `json:"requires_shipping" gorm:"default:true"`
	Taxable             bool      `json:"taxable" gorm:"default:true"`
	
	// Fulfillment
	FulfillmentService  string    `json:"fulfillment_service" gorm:"default:'manual'"`
	
	// Metadata
	Position            int       `json:"position" gorm:"default:1"`
	Metadata            map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// TableName returns the table name for Product
func (Product) TableName() string {
	return "products"
}

// TableName returns the table name for ProductVariant
func (ProductVariant) TableName() string {
	return "product_variants"
}
