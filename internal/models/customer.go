package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Customer represents a customer entity
type Customer struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email       string    `json:"email" gorm:"uniqueIndex;not null"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Phone       string    `json:"phone"`
	
	// Customer status
	AcceptsMarketing bool   `json:"accepts_marketing" gorm:"default:false"`
	State           string `json:"state" gorm:"default:'enabled'"` // enabled, disabled, invited, declined
	
	// Default addresses
	DefaultAddress Address `json:"default_address" gorm:"embedded;embeddedPrefix:default_"`
	
	// Statistics
	OrdersCount    int     `json:"orders_count" gorm:"default:0"`
	TotalSpent     float64 `json:"total_spent" gorm:"type:decimal(10,2);default:0"`
	
	// Metadata
	Tags           []string              `json:"tags" gorm:"type:text[]"`
	Note           string                `json:"note"`
	Metadata       map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// TableName returns the table name for Customer
func (Customer) TableName() string {
	return "customers"
}
