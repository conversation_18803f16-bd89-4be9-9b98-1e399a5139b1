package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrderStatus represents the different statuses an order can have
type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusConfirmed  OrderStatus = "confirmed"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusShipped    OrderStatus = "shipped"
	OrderStatusDelivered  OrderStatus = "delivered"
	OrderStatusCancelled  OrderStatus = "cancelled"
	OrderStatusRefunded   OrderStatus = "refunded"
)

// FulfillmentStatus represents the fulfillment status
type FulfillmentStatus string

const (
	FulfillmentStatusPending    FulfillmentStatus = "pending"
	FulfillmentStatusProcessing FulfillmentStatus = "processing"
	FulfillmentStatusFulfilled  FulfillmentStatus = "fulfilled"
	FulfillmentStatusPartial    FulfillmentStatus = "partial"
	FulfillmentStatusCancelled  FulfillmentStatus = "cancelled"
)

// ShippingStatus represents the shipping status
type ShippingStatus string

const (
	ShippingStatusPending   ShippingStatus = "pending"
	ShippingStatusPreparing ShippingStatus = "preparing"
	ShippingStatusShipped   ShippingStatus = "shipped"
	ShippingStatusInTransit ShippingStatus = "in_transit"
	ShippingStatusDelivered ShippingStatus = "delivered"
	ShippingStatusReturned  ShippingStatus = "returned"
)

// FinancialStatus represents the financial/payment status
type FinancialStatus string

const (
	FinancialStatusPending    FinancialStatus = "pending"
	FinancialStatusAuthorized FinancialStatus = "authorized"
	FinancialStatusPaid       FinancialStatus = "paid"
	FinancialStatusPartialPaid FinancialStatus = "partial_paid"
	FinancialStatusRefunded   FinancialStatus = "refunded"
	FinancialStatusVoided     FinancialStatus = "voided"
)

// Order represents the main order entity
type Order struct {
	ID                uuid.UUID         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrderNumber       string            `json:"order_number" gorm:"uniqueIndex;not null"`
	CustomerID        uuid.UUID         `json:"customer_id" gorm:"type:uuid;not null;index"`
	CustomerEmail     string            `json:"customer_email" gorm:"not null;index"`
	CustomerPhone     string            `json:"customer_phone"`
	Status            OrderStatus       `json:"status" gorm:"not null;default:'pending';index"`
	FulfillmentStatus FulfillmentStatus `json:"fulfillment_status" gorm:"not null;default:'pending';index"`
	ShippingStatus    ShippingStatus    `json:"shipping_status" gorm:"not null;default:'pending';index"`
	FinancialStatus   FinancialStatus   `json:"financial_status" gorm:"not null;default:'pending';index"`
	
	// Pricing
	SubtotalAmount    float64 `json:"subtotal_amount" gorm:"type:decimal(10,2);not null"`
	TaxAmount         float64 `json:"tax_amount" gorm:"type:decimal(10,2);default:0"`
	ShippingAmount    float64 `json:"shipping_amount" gorm:"type:decimal(10,2);default:0"`
	DiscountAmount    float64 `json:"discount_amount" gorm:"type:decimal(10,2);default:0"`
	TotalAmount       float64 `json:"total_amount" gorm:"type:decimal(10,2);not null"`
	Currency          string  `json:"currency" gorm:"not null;default:'USD'"`
	
	// Addresses
	BillingAddress  Address `json:"billing_address" gorm:"embedded;embeddedPrefix:billing_"`
	ShippingAddress Address `json:"shipping_address" gorm:"embedded;embeddedPrefix:shipping_"`
	
	// Relationships
	Items           []OrderItem      `json:"items" gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE"`
	StatusHistory   []OrderStatusHistory `json:"status_history" gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE"`
	
	// Metadata
	Notes           string                 `json:"notes"`
	Tags            []string              `json:"tags" gorm:"type:text[]"`
	Metadata        map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// Address represents billing and shipping addresses
type Address struct {
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	Company     string `json:"company"`
	Address1    string `json:"address1"`
	Address2    string `json:"address2"`
	City        string `json:"city"`
	Province    string `json:"province"`
	Country     string `json:"country"`
	PostalCode  string `json:"postal_code"`
	Phone       string `json:"phone"`
}

// OrderItem represents individual items in an order
type OrderItem struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrderID         uuid.UUID `json:"order_id" gorm:"type:uuid;not null;index"`
	ProductID       uuid.UUID `json:"product_id" gorm:"type:uuid;not null;index"`
	VariantID       uuid.UUID `json:"variant_id" gorm:"type:uuid;index"`
	SKU             string    `json:"sku" gorm:"index"`
	ProductTitle    string    `json:"product_title" gorm:"not null"`
	VariantTitle    string    `json:"variant_title"`
	Quantity        int       `json:"quantity" gorm:"not null;check:quantity > 0"`
	Price           float64   `json:"price" gorm:"type:decimal(10,2);not null"`
	CompareAtPrice  float64   `json:"compare_at_price" gorm:"type:decimal(10,2)"`
	TotalPrice      float64   `json:"total_price" gorm:"type:decimal(10,2);not null"`
	
	// Fulfillment tracking
	FulfillmentStatus FulfillmentStatus `json:"fulfillment_status" gorm:"not null;default:'pending'"`
	FulfilledQuantity int              `json:"fulfilled_quantity" gorm:"default:0"`
	
	// Product details
	Weight          float64                `json:"weight"`
	WeightUnit      string                 `json:"weight_unit" gorm:"default:'kg'"`
	RequiresShipping bool                  `json:"requires_shipping" gorm:"default:true"`
	Taxable         bool                   `json:"taxable" gorm:"default:true"`
	
	// Metadata
	Properties      map[string]interface{} `json:"properties" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// OrderStatusHistory tracks status changes
type OrderStatusHistory struct {
	ID              uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrderID         uuid.UUID       `json:"order_id" gorm:"type:uuid;not null;index"`
	StatusType      string          `json:"status_type" gorm:"not null"` // order, fulfillment, shipping, financial
	PreviousStatus  string          `json:"previous_status"`
	NewStatus       string          `json:"new_status" gorm:"not null"`
	Comment         string          `json:"comment"`
	ChangedBy       uuid.UUID       `json:"changed_by" gorm:"type:uuid"`
	ChangedByType   string          `json:"changed_by_type"` // user, system, api
	
	// Timestamps
	CreatedAt       time.Time       `json:"created_at"`
}

// BeforeCreate hook to generate order number
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.OrderNumber == "" {
		// Generate order number with timestamp and random suffix
		timestamp := time.Now().Format("20060102")
		o.OrderNumber = "ORD-" + timestamp + "-" + uuid.New().String()[:8]
	}
	return nil
}

// CalculateTotal calculates the total amount for the order
func (o *Order) CalculateTotal() {
	o.TotalAmount = o.SubtotalAmount + o.TaxAmount + o.ShippingAmount - o.DiscountAmount
}

// CalculateItemTotal calculates the total price for an order item
func (oi *OrderItem) CalculateItemTotal() {
	oi.TotalPrice = oi.Price * float64(oi.Quantity)
}

// TableName returns the table name for Order
func (Order) TableName() string {
	return "orders"
}

// TableName returns the table name for OrderItem
func (OrderItem) TableName() string {
	return "order_items"
}

// TableName returns the table name for OrderStatusHistory
func (OrderStatusHistory) TableName() string {
	return "order_status_history"
}
