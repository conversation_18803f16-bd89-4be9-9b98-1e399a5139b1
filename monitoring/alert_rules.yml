# Alert rules for Order Management Service

groups:
  - name: order-service-alerts
    rules:
      # Service availability alerts
      - alert: OrderServiceDown
        expr: up{job="order-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: order-service
        annotations:
          summary: "Order Service is down"
          description: "Order Service has been down for more than 1 minute"

      - alert: OrderServiceHealthCheckFailing
        expr: probe_success{job="order-service-health"} == 0
        for: 2m
        labels:
          severity: critical
          service: order-service
        annotations:
          summary: "Order Service health check failing"
          description: "Order Service health check has been failing for more than 2 minutes"

      # Performance alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="order-service"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 1 second for 5 minutes"

      - alert: HighErrorRate
        expr: rate(http_requests_total{job="order-service",status=~"5.."}[5m]) / rate(http_requests_total{job="order-service"}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for 5 minutes"

      # Resource usage alerts
      - alert: HighMemoryUsage
        expr: go_memstats_alloc_bytes{job="order-service"} / go_memstats_sys_bytes{job="order-service"} > 0.8
        for: 10m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 80% for 10 minutes"

      - alert: HighGoroutineCount
        expr: go_goroutines{job="order-service"} > 1000
        for: 5m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High goroutine count"
          description: "Goroutine count is above 1000 for 5 minutes"

      # Database alerts
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends{job="postgres"} > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High database connections"
          description: "Database connection count is above 80 for 5 minutes"

      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "Database is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: SlowDatabaseQueries
        expr: rate(pg_stat_database_tup_returned{job="postgres"}[5m]) / rate(pg_stat_database_tup_fetched{job="postgres"}[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "Slow database queries detected"
          description: "Database query efficiency is below 10% for 10 minutes"

      # Business logic alerts
      - alert: OrderCreationFailureRate
        expr: rate(order_creation_failures_total[5m]) / rate(order_creation_attempts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: order-service
        annotations:
          summary: "High order creation failure rate"
          description: "Order creation failure rate is above 10% for 5 minutes"

      - alert: PaymentProcessingFailures
        expr: rate(payment_processing_failures_total[5m]) > 10
        for: 2m
        labels:
          severity: critical
          service: order-service
        annotations:
          summary: "High payment processing failures"
          description: "Payment processing failures are above 10 per minute for 2 minutes"

      # System resource alerts
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="order-service"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for 10 minutes"

      - alert: DiskSpaceLow
        expr: node_filesystem_avail_bytes{job="node",mountpoint="/"} / node_filesystem_size_bytes{job="node",mountpoint="/"} < 0.1
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10% for 5 minutes"

      # Redis alerts (if using Redis)
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is above 90% for 5 minutes"

  - name: order-service-business-alerts
    rules:
      # Business-specific alerts
      - alert: UnusualOrderVolume
        expr: rate(orders_created_total[1h]) > 1000
        for: 5m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "Unusual order volume detected"
          description: "Order creation rate is above 1000 per hour"

      - alert: LowOrderVolume
        expr: rate(orders_created_total[1h]) < 10
        for: 30m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "Low order volume detected"
          description: "Order creation rate is below 10 per hour for 30 minutes"

      - alert: HighOrderCancellationRate
        expr: rate(orders_cancelled_total[1h]) / rate(orders_created_total[1h]) > 0.2
        for: 15m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "High order cancellation rate"
          description: "Order cancellation rate is above 20% for 15 minutes"

      - alert: InventoryShortage
        expr: inventory_shortage_alerts_total > 0
        for: 1m
        labels:
          severity: warning
          service: order-service
        annotations:
          summary: "Inventory shortage detected"
          description: "One or more products are out of stock"
