# Prometheus configuration for Order Management Service

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'order-service-monitor'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'order-service'
    static_configs:
      - targets: ['order-service:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Health check monitoring
  - job_name: 'order-service-health'
    static_configs:
      - targets: ['order-service:8080']
    metrics_path: '/health'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Database monitoring (if PostgreSQL exporter is available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s
    scrape_timeout: 5s

  # Redis monitoring (if Redis exporter is available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    scrape_timeout: 5s

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    scrape_timeout: 5s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    scrape_timeout: 5s

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration
# remote_read:
#   - url: "https://your-remote-storage/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
