-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    phone VARCHAR(50),
    accepts_marketing BOOLEAN DEFAULT FALSE,
    state VARCHAR(50) DEFAULT 'enabled',
    
    -- Default address fields
    default_first_name VA<PERSON><PERSON><PERSON>(255),
    default_last_name VA<PERSON><PERSON><PERSON>(255),
    default_company VARCHAR(255),
    default_address1 VARCHAR(255),
    default_address2 VARCHAR(255),
    default_city VARCHAR(255),
    default_province VARCHAR(255),
    default_country VARCHAR(255),
    default_postal_code VARCHAR(50),
    default_phone VARCHAR(50),
    
    -- Statistics
    orders_count INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    
    -- Metadata
    tags TEXT[],
    note TEXT,
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_state ON customers(state);
CREATE INDEX IF NOT EXISTS idx_customers_deleted_at ON customers(deleted_at);
CREATE INDEX IF NOT EXISTS idx_customers_created_at ON customers(created_at);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
